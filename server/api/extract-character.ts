export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { wikiContent, wikiUrl } = body;

    if (!wikiContent) {
      throw createError({
        statusCode: 400,
        message: 'Wiki content is required'
      });
    }

    // Call the Supabase edge function for character extraction
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw createError({
        statusCode: 500,
        message: 'Supabase configuration missing'
      });
    }

    const response = await fetch(`${supabaseUrl}/functions/v1/extract-character`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`
      },
      body: JSON.stringify({
        wikiContent,
        wikiUrl
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw createError({
        statusCode: response.status,
        message: `Character extraction failed: ${errorText}`
      });
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Character extraction error:', error);
    
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Failed to extract character data'
    });
  }
});
