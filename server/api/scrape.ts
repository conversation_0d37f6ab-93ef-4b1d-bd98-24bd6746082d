import axios from 'axios';
import * as cheerio from 'cheerio';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { url, extractCharacter = false } = body;

    if (!url) {
      throw createError({
        statusCode: 400,
        message: 'URL is required'
      });
    }

    // Fetch the webpage content using Firecrawl-like approach
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    const html = response.data;

    // If character extraction is requested, process with AI
    if (extract<PERSON>haracter) {
      const characterData = await extractCharacterWithAI(html, url);
      return {
        success: true,
        data: characterData
      };
    }

    // Otherwise return raw HTML
    return {
      success: true,
      data: {
        html,
        url
      }
    };
  } catch (error) {
    console.error('Scraping error:', error);

    throw createError({
      statusCode: error.response?.status || 500,
      message: error.message || 'Failed to scrape content'
    });
  }
});

// AI-powered character extraction using Pica + Firecrawl approach
async function extractCharacterWithAI(html: string, url: string) {
  try {
    // Step 1: Clean and extract text content using Cheerio (Firecrawl-like processing)
    const $ = cheerio.load(html);

    // Remove unwanted elements
    $('script, style, nav, footer, .mw-editsection, .reference, sup.reference, .error').remove();

    // Extract main content
    const contentSelectors = [
      '#mw-content-text',
      '.mw-parser-output',
      'main',
      '.content',
      'article',
      '.post-content'
    ];

    let mainContent = '';
    for (const selector of contentSelectors) {
      const content = $(selector).text();
      if (content && content.length > mainContent.length) {
        mainContent = content;
      }
    }

    // Fallback to body if no main content found
    if (!mainContent) {
      mainContent = $('body').text();
    }

    // Clean the content
    const cleanContent = mainContent
      .replace(/\n+/g, '\n')
      .replace(/\s+/g, ' ')
      .replace(/\[edit\]/g, '')
      .trim();

    // Step 2: Extract basic information using pattern matching
    const name = extractName($, url);
    const imageUrl = extractImage($, url);
    const description = extractDescription(cleanContent);
    const tags = extractTags(cleanContent, url);

    // Step 3: Use AI (Pica) for enhanced extraction if API key is available
    const config = useRuntimeConfig();
    let aiEnhancedData = null;

    if (config.public.aiApiKey) {
      aiEnhancedData = await enhanceWithPicaAI(cleanContent, name, config.public.aiApiKey);
    }

    // Combine extracted data with AI enhancements
    return {
      name: aiEnhancedData?.name || name,
      description: aiEnhancedData?.description || description,
      image_url: aiEnhancedData?.image_url || imageUrl,
      tags: aiEnhancedData?.tags || tags,
      wiki_url: url,
      // Include raw content for future processing
      raw_content: cleanContent.substring(0, 2000) // Limit size
    };

  } catch (error) {
    console.error('Character extraction error:', error);
    throw new Error('Failed to extract character data');
  }
}

// Helper function to extract character name
function extractName($: any, url: string): string {
  // Try different selectors for the name
  const nameSelectors = [
    '.firstHeading',
    'h1.entry-title',
    'h1',
    '.page-title',
    '.character-name'
  ];

  for (const selector of nameSelectors) {
    const name = $(selector).first().text().trim();
    if (name && name.length > 0 && name.length < 100) {
      return name;
    }
  }

  // Fallback to URL parsing
  const urlParts = url.split('/');
  const lastPart = urlParts[urlParts.length - 1];
  return lastPart ? lastPart.replace(/_/g, ' ').replace(/%20/g, ' ') : 'Unknown Character';
}

// Helper function to extract character image
function extractImage($: any, url: string): string {
  // Try different selectors for character images
  const imageSelectors = [
    '.infobox img',
    '.character-image img',
    '.thumb img',
    '.image img',
    'img[alt*="portrait"]',
    'img[alt*="character"]',
    '.mw-file-description img',
    'table.infobox img'
  ];

  for (const selector of imageSelectors) {
    const img = $(selector).first();
    if (img.length > 0) {
      let src = img.attr('src') || img.attr('data-src');
      if (src) {
        // Handle relative URLs
        if (src.startsWith('//')) {
          src = 'https:' + src;
        } else if (src.startsWith('/')) {
          const baseUrl = new URL(url);
          src = baseUrl.origin + src;
        }

        // Avoid tiny icons or thumbnails
        if (!src.includes('icon') && !src.includes('thumb/') &&
            (src.includes('.jpg') || src.includes('.png') || src.includes('.jpeg'))) {
          return src;
        }
      }
    }
  }

  return ''; // No suitable image found
}

// Helper function to extract comprehensive description (resume-style)
function extractDescription(content: string): string {
  // Look for substantial paragraphs and combine them into a comprehensive description
  const paragraphs = content.split('\n')
    .filter(p => p.trim().length > 30)
    .slice(0, 5); // Take first 5 substantial paragraphs

  if (paragraphs.length > 0) {
    // Combine paragraphs into a comprehensive description
    const combinedText = paragraphs.join(' ').substring(0, 1200).trim();
    return combinedText || 'No description available.';
  }

  return 'No description available.';
}

// Helper function to extract tags
function extractTags(content: string, url: string): string[] {
  const tags = new Set<string>();
  const lowerContent = content.toLowerCase();

  // Common character types and attributes
  const commonTags = [
    'detective', 'hero', 'villain', 'protagonist', 'antagonist',
    'fictional', 'character', 'human', 'superhero', 'wizard',
    'scientist', 'doctor', 'professor', 'student', 'warrior',
    'knight', 'prince', 'princess', 'king', 'queen',
    'magical', 'supernatural', 'alien', 'robot', 'android'
  ];

  // Add tags based on content
  commonTags.forEach(tag => {
    if (lowerContent.includes(tag)) {
      tags.add(tag);
    }
  });

  // Add source-based tags
  if (url.includes('wikipedia')) tags.add('wikipedia');
  if (url.includes('fandom')) tags.add('fandom');
  if (url.includes('marvel')) tags.add('marvel');
  if (url.includes('dc')) tags.add('dc');

  // Ensure we have at least some basic tags
  if (tags.size === 0) {
    tags.add('fictional');
    tags.add('character');
  }

  return Array.from(tags);
}

// AI enhancement using Pica
async function enhanceWithPicaAI(content: string, baseName: string, apiKey: string) {
  try {
    // Prepare the prompt for character extraction
    const prompt = `
Extract character information from the following text. Create a comprehensive character resume/profile. Return a JSON object with these fields:
- name: The character's full name
- description: A comprehensive resume-style description (3-4 paragraphs) covering: who they are, their background, key characteristics, abilities/skills, notable achievements, and relationships. Make it detailed and informative like a character profile.
- image_url: If you can identify an image URL in the text, include it (optional)
- tags: An array of relevant tags describing the character (max 10 tags)

Text content:
${content.substring(0, 3000)}

Base name found: ${baseName}

Make the description comprehensive and detailed, like a character resume that covers all important aspects of who they are.
Return only valid JSON, no additional text.`;

    // Call Pica AI API (adjust endpoint and format as needed)
    const response = await fetch('https://api.pica.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at extracting character information from text. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      console.warn('Pica AI request failed:', response.status);
      return null;
    }

    const result = await response.json();
    const aiResponse = result.choices?.[0]?.message?.content;

    if (!aiResponse) {
      console.warn('No AI response received');
      return null;
    }

    // Parse the JSON response
    try {
      const parsedData = JSON.parse(aiResponse);

      // Validate the response structure
      if (parsedData.name && parsedData.description) {
        return {
          name: parsedData.name,
          description: parsedData.description,
          image_url: parsedData.image_url || '',
          tags: Array.isArray(parsedData.tags) ? parsedData.tags : []
        };
      }
    } catch (parseError) {
      console.warn('Failed to parse AI response as JSON:', parseError);
    }

    return null;
  } catch (error) {
    console.warn('AI enhancement failed:', error);
    return null; // Graceful fallback to basic extraction
  }
}