import axios from 'axios';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { url } = body;

    if (!url) {
      throw createError({
        statusCode: 400,
        message: 'URL is required'
      });
    }

    // Fetch the webpage content
    const response = await axios.get(url);
    const html = response.data;

    return {
      success: true,
      data: {
        html,
        url
      }
    };
  } catch (error) {
    console.error('Scraping error:', error);
    
    throw createError({
      statusCode: error.response?.status || 500,
      message: error.message || 'Failed to scrape content'
    });
  }
});