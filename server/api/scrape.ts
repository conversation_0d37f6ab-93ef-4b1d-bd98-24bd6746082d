import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import axios from 'axios'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    console.log('Request body:', body)

    const { url, extractCharacter = false } = body
    if (!url) {
      console.error('No URL provided')
      throw createError({ statusCode: 400, message: 'URL is required' })
    }

    console.log('Processing URL:', url, 'Extract character:', extract<PERSON>haracter)

    // If character extraction is requested, use Pica's passthrough APIs
    if (extractCharacter) {
      console.log('Starting character extraction...')

      // Check environment variables
      if (!process.env.PICA_SECRET_KEY) {
        throw createError({ statusCode: 500, message: 'PICA_SECRET_KEY not configured' })
      }
      if (!process.env.PICA_FIRECRAWL_CONNECTION_KEY) {
        throw createError({ statusCode: 500, message: 'PICA_FIRECRAWL_CONNECTION_KEY not configured' })
      }

      // 1. Scrape with Firecrawl via Pica passthrough
      console.log('Calling Firecrawl via Pica...')
      const scrapeRes = await axios.post(
        'https://api.picaos.com/v1/passthrough/v1/scrape',
        {
          url,
          formats: ['markdown'],
          onlyMainContent: true
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-pica-secret': process.env.PICA_SECRET_KEY,
            'x-pica-connection-key': process.env.PICA_FIRECRAWL_CONNECTION_KEY,
            
          }
        }
      )

      console.log('Firecrawl response status:', scrapeRes.status)
      console.log('Firecrawl response data keys:', Object.keys(scrapeRes.data || {}))

      const markdownContent = scrapeRes.data.data?.markdown || ''
      const metadata = scrapeRes.data.data?.metadata || {}
      const characterImage = metadata?.ogImage || metadata?.image || ''

      // 2. Prepare prompt for Pica
      const prompt = `
Extract character information from the following text. Create a comprehensive character resume/profile. Return a JSON object with these fields:
- name: The character's full name
- description: A comprehensive resume-style description covering: who they are, their background, key characteristics, abilities/skills, notable achievements, and relationships. IMPORTANT: Keep this description to EXACTLY 1200 characters or less. Make every word count with actual information about the character.
- image_url: If you can identify an image URL in the text, include it (optional)
- tags: An array of relevant tags describing the character (max 10 tags)

Text content:
${markdownContent.slice(0, 3000)}

Character image found: ${characterImage}

CRITICAL: The description must be no longer than 1200 characters. Focus on actual character information, not filler text.
Return only valid JSON, no additional text.
      `.trim()

      // 3. Extract character info with OpenAI via Pica passthrough
      console.log('Calling OpenAI via Pica...')

      if (!process.env.PICA_OPENAI_CONNECTION_KEY) {
        throw createError({ statusCode: 500, message: 'PICA_OPENAI_CONNECTION_KEY not configured' })
      }

      const aiRes = await axios.post(
        'https://api.picaos.com/v1/passthrough/v1/chat/completions',
        {
          model: 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: 'You are an expert at extracting character information from text. Always return valid JSON.' },
            { role: 'user', content: prompt }
          ],
          max_tokens: 1000,
          temperature: 0.3
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-pica-secret': process.env.PICA_SECRET_KEY,
            'x-pica-connection-key': process.env.PICA_OPENAI_CONNECTION_KEY,
            
          }
        }
      )

      console.log('OpenAI response status:', aiRes.status)
      console.log('OpenAI response data keys:', Object.keys(aiRes.data || {}))

      // 4. Parse and return the AI result
      const aiContent = aiRes.data.choices?.[0]?.message?.content
      console.log('AI Response:', aiContent)

      let character
      try {
        character = JSON.parse(aiContent)
        // Use the character image if AI didn't find one
        if (!character.image_url && characterImage) {
          character.image_url = characterImage
        }
        // Add wiki URL for future use
        character.wiki_url = url

        console.log('Parsed character:', character)
      } catch (e) {
        console.error('Failed to parse AI response:', e)
        console.error('AI Content:', aiContent)
        throw createError({ statusCode: 500, message: 'AI response was not valid JSON.' })
      }

      // Return structured JSON response
      return {
        success: true,
        data: {
          character,
          scraped_data: {
            markdown: markdownContent.slice(0, 1000), // First 1000 chars for reference
            metadata,
            url,
            timestamp: new Date().toISOString()
          }
        }
      }
    }

    // Otherwise return basic scraping (fallback for other uses)
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    return {
      success: true,
      data: {
        html: response.data,
        url
      }
    };
  } catch (error: any) {
    console.error('Error:', error)
    console.error('Error response:', error.response?.data)
    console.error('Error status:', error.response?.status)
    console.error('Error headers:', error.response?.headers)

    throw createError({
      statusCode: error.statusCode || error.response?.status || 500,
      message: error.response?.data?.message || error.message || 'Failed to process character extraction'
    })
  }
})