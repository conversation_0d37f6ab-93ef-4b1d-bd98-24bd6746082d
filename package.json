{"name": "mystery-minds", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "HOST=0.0.0.0 nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxtjs/supabase": "^1.1.6", "@pinia/nuxt": "^0.5.1", "@supabase/supabase-js": "^2.39.7", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "nuxt": "^3.13.0", "pica": "^9.0.1", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0", "zod": "^3.22.4"}, "devDependencies": {"@iconify/vue": "^4.1.1", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0-alpha.6", "daisyui": "^5.0.0", "tailwindcss": "^4.0.0-alpha.6", "typescript": "^5.4.2", "vue-tsc": "^1.8.27"}}