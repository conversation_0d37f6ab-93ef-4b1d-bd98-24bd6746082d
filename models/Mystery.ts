import { z } from 'zod';

// Validation schema for mystery
export const mysterySchema = z.object({
  id: z.string().uuid().optional(),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  category: z.string().min(1, 'Category is required'),
  created_by: z.string().uuid().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
  view_count: z.number().optional()
});

export type Mystery = z.infer<typeof mysterySchema>;

// Validation schema for analysis
export const analysisSchema = z.object({
  id: z.string().uuid().optional(),
  mystery_id: z.string().uuid(),
  summary: z.string().min(1, 'Summary is required'),
  detailed_analysis: z.string().min(1, 'Detailed analysis is required'),
  confidence: z.number().min(0).max(1),
  evolution: z.string().optional(),
  created_at: z.date().optional(),
  created_by: z.string().uuid().optional(),
  sources: z.array(
    z.object({
      title: z.string().min(1, 'Source title is required'),
      description: z.string().optional()
    })
  ).optional()
});

export type Analysis = z.infer<typeof analysisSchema>;

// Validation schema for comment
export const commentSchema = z.object({
  id: z.string().uuid().optional(),
  mystery_id: z.string().uuid(),
  user_id: z.string().uuid().optional(),
  content: z.string().min(1, 'Comment content is required'),
  evidence: z.string().optional(),
  likes: z.number().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional()
});

export type Comment = z.infer<typeof commentSchema>;

// Validation schema for evidence
export const evidenceSchema = z.object({
  id: z.string().uuid().optional(),
  mystery_id: z.string().uuid(),
  title: z.string().min(1, 'Evidence title is required'),
  description: z.string().min(1, 'Evidence description is required'),
  impact: z.number().optional(),
  added_by: z.string().uuid().optional(),
  created_at: z.date().optional()
});

export type Evidence = z.infer<typeof evidenceSchema>;