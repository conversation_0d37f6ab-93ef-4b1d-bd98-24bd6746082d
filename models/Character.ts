import { z } from 'zod';

// Validation schema for character
export const characterSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(50, 'Character profile must be at least 50 characters').max(1200, 'Character profile must not exceed 1200 characters'),
  image_url: z.string().url('Invalid image URL').optional().or(z.literal('')),
  wiki_url: z.string().url('Invalid wiki URL').optional().or(z.literal('')),
  tags: z.array(z.string()).optional(),
  attributes: z.record(z.string(), z.string()).optional(),
  created_by: z.string().uuid().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional()
});

export type Character = z.infer<typeof characterSchema>;

// Validation schema for character extraction
export const characterExtractionSchema = z.object({
  wikiContent: z.string().min(10, 'Wiki content must be at least 10 characters'),
  wikiUrl: z.string().url('Invalid wiki URL').optional().or(z.literal(''))
});

export type CharacterExtraction = z.infer<typeof characterExtractionSchema>;