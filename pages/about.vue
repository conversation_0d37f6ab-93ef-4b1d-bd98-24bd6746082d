<template>
  <div>
    <h1 class="text-4xl font-bold mb-8 font-heading">About Mystery<span class="text-accent">Minds</span></h1>
    
    <div class="prose prose-lg prose-invert max-w-none">
      <p>
        MysteryMinds is an AI-powered platform for collaborative investigation of fictional mysteries. 
        Our platform combines advanced AI analysis with community participation to create a unique 
        experience for mystery enthusiasts.
      </p>
      
      <h2>Our Mission</h2>
      <p>
        To create a vibrant community where fiction lovers can explore, analyze, and solve 
        mysteries together, leveraging both human intuition and artificial intelligence.
      </p>
      
      <h2>How It Works</h2>
      <div class="bg-base-300 p-6 rounded-lg my-6">
        <ol class="list-decimal list-inside space-y-4">
          <li>
            <strong>Character Profiles</strong>: Upload wiki content about fictional characters, and our AI will extract 
            key information to create comprehensive character profiles.
          </li>
          <li>
            <strong>Mystery Creation</strong>: Create discussion topics for specific mysteries related to 
            characters and their stories.
          </li>
          <li>
            <strong>AI Analysis</strong>: Our AI analyzes the mysteries using available data, providing insights 
            with confidence scoring and reasoning.
          </li>
          <li>
            <strong>Community Participation</strong>: Users can comment, provide additional evidence, and 
            discuss theories collaboratively.
          </li>
          <li>
            <strong>Evolving Investigations</strong>: As new information becomes available, users can re-run 
            AI analysis to see how the reasoning evolves.
          </li>
        </ol>
      </div>
      
      <h2>Technology Behind MysteryMinds</h2>
      <p>
        Our platform is built using cutting-edge technologies:
      </p>
      <ul class="list-disc list-inside">
        <li>Nuxt.js and Vue.js for a smooth, responsive user interface</li>
        <li>Tailwind CSS and DaisyUI for a beautiful, customized design</li>
        <li>Supabase for secure, scalable database management</li>
        <li>Pica and Firecrawl AI for data extraction and analysis</li>
        <li>Zod for robust data validation</li>
      </ul>
      
      <h2>Join Our Community</h2>
      <p>
        Whether you're a casual mystery reader or a dedicated sleuth, MysteryMinds provides a 
        space for you to explore fictional mysteries in a new, interactive way. Sign up today 
        to start your investigation journey!
      </p>
    </div>
    
    <div class="mt-12 flex justify-center">
      <NuxtLink to="/auth/login" class="btn btn-primary btn-lg">
        Get Started
      </NuxtLink>
    </div>
  </div>
</template>