<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="card bg-base-300 w-full max-w-md shadow-xl">
      <div class="card-body">
        <h2 class="card-title font-heading text-2xl mb-6">
          {{ activeTab === 'login' ? 'Sign In' : 'Create Account' }}
        </h2>
        
        <div class="tabs tabs-boxed mb-6">
          <a 
            @click="activeTab = 'login'" 
            :class="['tab', activeTab === 'login' ? 'tab-active' : '']"
          >
            Sign In
          </a>
          <a 
            @click="activeTab = 'signup'" 
            :class="['tab', activeTab === 'signup' ? 'tab-active' : '']"
          >
            Sign Up
          </a>
        </div>

        <form @submit.prevent="activeTab === 'login' ? handleLogin() : handleSignUp()">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Email</span>
            </label>
            <input 
              type="email" 
              v-model="email" 
              placeholder="<EMAIL>" 
              class="input input-bordered" 
              required 
            />
          </div>

          <!-- Add username field after email (only for signup) -->
          <div v-if="activeTab === 'signup'" class="form-control mt-4">
            <label class="label">
              <span class="label-text">Username</span>
            </label>
            <input 
              type="text" 
              v-model="username" 
              placeholder="username" 
              class="input input-bordered" 
              required 
            />
          </div>

          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Password</span>
            </label>
            <div class="relative">
              <input 
                :type="showPassword ? 'text' : 'password'" 
                v-model="password" 
                placeholder="••••••••" 
                class="input input-bordered w-full pr-10" 
                required 
              />
              <button 
                type="button"
                @click="showPassword = !showPassword" 
                class="absolute inset-y-0 right-0 px-3 flex items-center"
              >
                <iconify-icon :icon="showPassword ? 'solar:eye-bold' : 'solar:eye-closed-bold'" width="20" height="20"></iconify-icon>
              </button>
            </div>
          </div>

          <!-- Password Requirements (Sign Up only) -->
          <div v-if="activeTab === 'signup'" class="mt-4 space-y-2 text-sm">
            <div class="flex items-center gap-2">
              <iconify-icon 
                :icon="hasMinLength ? 'solar:check-circle-bold' : 'solar:close-circle-bold'"
                :class="hasMinLength ? 'text-success' : 'text-error'"
                width="16" 
                height="16"
              ></iconify-icon>
              <span>
                {{ remainingChars > 0 
                  ? `${remainingChars} more characters needed` 
                  : 'Minimum length met' }}
              </span>
            </div>
            <div class="flex items-center gap-2">
              <iconify-icon 
                :icon="hasNumber ? 'solar:check-circle-bold' : 'solar:close-circle-bold'"
                :class="hasNumber ? 'text-success' : 'text-error'"
                width="16" 
                height="16"
              ></iconify-icon>
              <span>Contains a number</span>
            </div>
            <div class="flex items-center gap-2">
              <iconify-icon 
                :icon="hasUppercase ? 'solar:check-circle-bold' : 'solar:close-circle-bold'"
                :class="hasUppercase ? 'text-success' : 'text-error'"
                width="16" 
                height="16"
              ></iconify-icon>
              <span>Contains an uppercase letter</span>
            </div>

            <!-- Password Strength Meter -->
            <div class="mt-4">
              <div class="text-xs mb-1">Password Strength</div>
              <div class="h-2 rounded-full bg-base-200">
                <div 
                  :class="['h-full rounded-full transition-all', strengthClass]"
                  :style="{ width: `${passwordStrength}%` }"
                ></div>
              </div>
            </div>
          </div>

          <div v-if="errorMsg" class="alert alert-error mt-6">
            <span>{{ errorMsg }}</span>
          </div>

          <div v-if="successMsg" class="alert alert-success mt-6">
            <span>{{ successMsg }}</span>
          </div>

          <div class="form-control mt-6">
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="loading || (activeTab === 'signup' && !isValidPassword)"
            >
              <span v-if="loading" class="loading loading-spinner loading-sm mr-2"></span>
              {{ activeTab === 'login' ? 'Sign In' : 'Create Account' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
const client = useSupabaseClient();
const user = useSupabaseUser();
const router = useRouter();

const activeTab = ref('login');
const email = ref('');
const password = ref('');
const loading = ref(false);
const errorMsg = ref('');
const showPassword = ref(false);
const username = ref('');
const successMsg = ref('');

// Password validation states
const hasMinLength = ref(false);
const hasNumber = ref(false);
const hasUppercase = ref(false);
const remainingChars = ref(8);

// Password strength calculation
const passwordStrength = computed(() => {
  if (!password.value) return 0;
  
  let strength = 0;
  
  // Length contribution (up to 40%)
  strength += Math.min(password.value.length * 5, 40);
  
  // Character variety contribution
  if (hasNumber.value) strength += 20;
  if (hasUppercase.value) strength += 20;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password.value)) strength += 20;
  
  return Math.min(strength, 100);
});

// Strength indicator class
const strengthClass = computed(() => {
  if (passwordStrength.value >= 80) return 'bg-success';
  if (passwordStrength.value >= 50) return 'bg-warning';
  return 'bg-error';
});

// Password validation
const validatePassword = () => {
  const pass = password.value;
  
  hasMinLength.value = pass.length >= 8;
  remainingChars.value = Math.max(8 - pass.length, 0);
  
  hasNumber.value = /\d/.test(pass);
  hasUppercase.value = /[A-Z]/.test(pass);
};

// Check if password meets all requirements
const isValidPassword = computed(() => {
  return hasMinLength.value && hasNumber.value && hasUppercase.value;
});

// If user is already logged in, redirect to profile page
watch(user, (newUser) => {
  if (newUser) {
    router.push('/profile');
  }
}, { immediate: true });

const handleLogin = async () => {
  if (!email.value || !password.value) return;
  
  loading.value = true;
  errorMsg.value = '';
  
  try {
    const { error } = await client.auth.signInWithPassword({
      email: email.value,
      password: password.value
    });
    
    if (error) throw error;
  } catch (error) {
    console.error('Error signing in:', error);
    errorMsg.value = error.message;
  } finally {
    loading.value = false;
  }
};

const handleSignUp = async () => {
  if (!email.value || !username.value || !isValidPassword.value) return;
  
  loading.value = true;
  errorMsg.value = '';
  
  try {
    // Sign up with Supabase Auth
    const { data: { user: newUser }, error: signUpError } = await client.auth.signUp({
      email: email.value,
      password: password.value,
      options: {
        data: {
          username: username.value, // Store username in auth metadata
          display_name: username.value
        }
      }
    });

    if (signUpError) throw signUpError;

    // Show verification message
    successMsg.value = "Please check your email to verify your account.";
    activeTab.value = 'login'; // Switch back to login tab
  } catch (error) {
    console.error('Error signing up:', error);
    errorMsg.value = error.message;
  } finally {
    loading.value = false;
  }
};

// Watch password changes for validation
watch(password, validatePassword);
</script>
