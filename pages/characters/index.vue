<template>
  <div>
    <h1 class="text-3xl font-bold mb-8 font-heading">Character Database</h1>

    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
      <div class="form-control w-full max-w-xs">
        <div class="input-group">
          <input type="text" v-model="searchQuery" placeholder="Search characters..." class="input input-bordered w-full" />
          <button class="btn btn-square">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </div>
      </div>
      <NuxtLink v-if="user" to="/characters/add" class="btn btn-primary">
        Add Character
      </NuxtLink>
      <NuxtLink v-else to="/auth/login" class="btn btn-primary">
        Login to Add Characters
      </NuxtLink>
    </div>

    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-dots loading-lg text-primary"></span>
    </div>
    
    <div v-else-if="error" class="alert alert-error">
      <span>{{ error }}</span>
    </div>
    
    <div v-else-if="filteredCharacters.length === 0" class="text-center py-8">
      <p class="text-slate-400">No characters found. Try a different search or add a new character.</p>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="character in filteredCharacters" :key="character.id" class="card bg-base-300 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
        <figure class="px-4 pt-4">
          <div class="w-full h-48 bg-base-200 rounded-xl overflow-hidden">
            <img :src="character.image_url" :alt="character.name" class="w-full h-full object-cover" />
          </div>
        </figure>
        <div class="card-body">
          <h2 class="card-title font-heading">{{ character.name }}</h2>
          <div class="flex flex-wrap gap-1 mb-2">
            <div v-for="(tag, index) in character.tags" :key="index" class="badge badge-primary badge-outline">
              {{ tag }}
            </div>
          </div>
          <p class="text-slate-300 line-clamp-3">{{ character.description }}</p>
          <div class="flex justify-end mt-4">
            <NuxtLink :to="`/characters/${character.id}`" class="btn btn-primary btn-sm">
              View Profile
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <div v-if="totalPages > 1" class="flex justify-center mt-8">
      <div class="btn-group">
        <button 
          v-for="page in totalPages" 
          :key="page" 
          @click="currentPage = page"
          :class="[
            'btn', 
            currentPage === page ? 'btn-active' : ''
          ]"
        >
          {{ page }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const client = useSupabaseClient();

const loading = ref(true);
const error = ref('');
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = 9;

const characters = ref([]);

async function fetchCharacters() {
  try {
    // Fetch characters from test_characters table
    const { data: chars, error: charError } = await client
      .from('characters')
      .select('*');
      
    if (charError) throw charError;
    
    // Fetch tags for each character
    const charactersWithTags = await Promise.all(chars.map(async (char) => {
      const { data: tags, error: tagError } = await client
        .from('character_tags')
        .select('tag')
        .eq('character_id', char.id);
        
      if (tagError) throw tagError;
      
      return {
        ...char,
        tags: tags.map(t => t.tag)
      };
    }));
    
    characters.value = charactersWithTags;
  } catch (e) {
    console.error('Error fetching characters:', e);
    error.value = e.message;
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchCharacters();
});

const filteredCharacters = computed(() => {
  let filtered = characters.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(character => 
      character.name.toLowerCase().includes(query) || 
      character.description?.toLowerCase().includes(query) ||
      character.tags?.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  // Pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return filtered.slice(start, end);
});

const totalPages = computed(() => {
  let filtered = characters.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(character => 
      character.name.toLowerCase().includes(query) || 
      character.description?.toLowerCase().includes(query) ||
      character.tags?.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  return Math.ceil(filtered.length / itemsPerPage);
});

// Reset to page 1 when search query changes
watch(searchQuery, () => {
  currentPage.value = 1;
});
</script>
