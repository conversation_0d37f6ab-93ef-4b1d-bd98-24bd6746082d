<template>
  <div>
    <h1 class="text-3xl font-bold mb-8 font-heading">Add New Character</h1>
    
    <div class="card bg-base-300 shadow-xl max-w-4xl mx-auto">
      <div class="card-body">
        <div class="tabs tabs-boxed mb-6">
          <a 
            @click="activeTab = 'wiki'" 
            :class="['tab', activeTab === 'wiki' ? 'tab-active' : '']"
          >
            Wiki Content
          </a>
          <a 
            @click="activeTab = 'manual'" 
            :class="['tab', activeTab === 'manual' ? 'tab-active' : '']"
          >
            Manual Entry
          </a>
        </div>
        
        <div v-if="activeTab === 'wiki'" class="space-y-6">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Wiki URL</span>
            </label>
            <div class="flex gap-2">
              <input 
                type="text" 
                v-model="wikiUrl" 
                placeholder="https://wikipedia.org/wiki/Character_Name" 
                class="input input-bordered flex-1" 
                :disabled="isProcessing"
              />
              <button 
                @click="fetchWikiContent" 
                class="btn btn-primary" 
                :disabled="!wikiUrl || isProcessing"
              >
                <span v-if="isProcessing" class="loading loading-spinner loading-sm mr-2"></span>
                Fetch
              </button>
            </div>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Wiki Content</span>
            </label>
            <textarea 
              v-model="wikiContent" 
              placeholder="Paste the character description and relevant information here..." 
              class="textarea textarea-bordered h-64"
              :disabled="isProcessing"
            ></textarea>
          </div>
          
          <div class="form-control">
            <button 
              @click="processWikiContent" 
              class="btn btn-primary" 
              :disabled="!wikiContent || isProcessing"
            >
              <span v-if="isProcessing" class="loading loading-spinner loading-sm mr-2"></span>
              {{ isProcessing ? 'Processing...' : 'Extract Character Data' }}
            </button>
          </div>
        </div>
        
        <div v-if="activeTab === 'manual' || showExtractedData" class="space-y-6">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Character Name</span>
            </label>
            <input type="text" v-model="characterData.name" placeholder="Character Name" class="input input-bordered w-full" />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Description</span>
            </label>
            <textarea 
              v-model="characterData.description" 
              placeholder="Character description..." 
              class="textarea textarea-bordered h-32"
            ></textarea>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Background</span>
            </label>
            <textarea 
              v-model="characterData.background" 
              placeholder="Character's background and history..." 
              class="textarea textarea-bordered h-32"
            ></textarea>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Tags (comma separated)</span>
            </label>
            <input 
              type="text" 
              v-model="tagsInput" 
              placeholder="detective, genius, mysterious" 
              class="input input-bordered w-full" 
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Image URL</span>
            </label>
            <input 
              type="text" 
              v-model="characterData.image_url" 
              placeholder="https://example.com/character-image.jpg" 
              class="input input-bordered w-full" 
            />
          </div>
          
          <div class="form-control mt-6">
            <button @click="saveCharacter" class="btn btn-primary" :disabled="isSaving">
              <span v-if="isSaving" class="loading loading-spinner loading-sm mr-2"></span>
              {{ isSaving ? 'Saving...' : 'Save Character' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { z } from 'zod';

definePageMeta({
  middleware: ['auth']
});

const router = useRouter();
const { scrapeUrl, loading: scraping } = useScraper();

const activeTab = ref('wiki');
const wikiUrl = ref('');
const wikiContent = ref('');
const isProcessing = ref(false);
const isSaving = ref(false);
const showExtractedData = ref(false);
const tagsInput = ref('');

const characterData = ref({
  name: '',
  description: '',
  background: '',
  image_url: '',
  tags: []
});

const fetchWikiContent = async () => {
  if (!wikiUrl.value) return;
  
  try {
    const scrapedData = await scrapeUrl(wikiUrl.value);
    wikiContent.value = scrapedData.mainContent;
    
    if (scrapedData.imageUrls.length > 0) {
      characterData.value.image_url = scrapedData.imageUrls[0];
    }
  } catch (error) {
    console.error('Error fetching wiki content:', error);
    alert('Failed to fetch content. Please try again or paste the content manually.');
  }
};

const processWikiContent = async () => {
  if (!wikiContent.value) return;
  
  isProcessing.value = true;
  
  try {
    // This would use the AI in a real implementation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock extracted data
    characterData.value = {
      name: 'Extracted Character Name',
      description: 'This is an automatically extracted description of the character based on the provided wiki content.',
      background: 'This is the background information extracted from the wiki content, detailing the character\'s history and important life events.',
      image_url: characterData.value.image_url || 'https://images.pexels.com/photos/7173056/pexels-photo-7173056.jpeg',
      tags: ['detective', 'fictional', 'mysterious']
    };
    
    tagsInput.value = characterData.value.tags.join(', ');
    showExtractedData.value = true;
    activeTab.value = 'manual'; // Switch to manual tab to show and edit extracted data
  } catch (error) {
    console.error('Error processing wiki content:', error);
    alert('Failed to process wiki content. Please try again or use manual entry.');
  } finally {
    isProcessing.value = false;
  }
};

// Validation schema using Zod
const characterSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  background: z.string().optional(),
  image_url: z.string().url('Invalid image URL').optional().or(z.literal('')),
  tags: z.array(z.string())
});

const saveCharacter = async () => {
  try {
    // Process tags
    characterData.value.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag);
    
    // Validate data
    const validatedData = characterSchema.parse(characterData.value);
    
    isSaving.value = true;
    
    const client = useSupabaseClient();
    const user = useSupabaseUser();
    
    // Insert character into the proper characters table
    const { data: character, error: charError } = await client
      .from('characters')
      .insert({
        name: characterData.value.name,
        description: characterData.value.description,
        background: characterData.value.background,
        image_url: characterData.value.image_url,
        wiki_url: wikiUrl.value,
        created_by: user.value?.id
      })
      .select()
      .single();

    if (charError) throw charError;

    // Insert tags
    if (characterData.value.tags.length > 0) {
      const tagInserts = characterData.value.tags.map(tag => ({
        character_id: character.id,
        tag
      }));

      const { error: tagError } = await client
        .from('character_tags')
        .insert(tagInserts);

      if (tagError) throw tagError;
    }
    
    // Navigate to the character page
    router.push(`/characters/${character.id}`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(e => e.message).join('\n');
      alert(`Validation error: ${errorMessage}`);
    } else {
      console.error('Error saving character:', error);
      alert('Failed to save character. Please try again.');
    }
  } finally {
    isSaving.value = false;
  }
};
</script>
