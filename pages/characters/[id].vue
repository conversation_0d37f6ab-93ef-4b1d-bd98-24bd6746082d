<template>
  <div>
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-dots loading-lg text-primary"></span>
    </div>
    
    <div v-else-if="error" class="alert alert-error">
      <span>{{ error }}</span>
    </div>
    
    <div v-else-if="character" class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Character Sidebar -->
      <div class="md:col-span-1">
        <div class="card bg-base-300 shadow-xl sticky top-4">
          <figure class="px-4 pt-4">
            <div class="w-full h-64 bg-base-200 rounded-xl overflow-hidden">
              <img 
                :src="character.image_url || 'https://images.pexels.com/photos/7173056/pexels-photo-7173056.jpeg'" 
                :alt="character.name" 
                class="w-full h-full object-cover" 
              />
            </div>
          </figure>
          <div class="card-body">
            <h2 class="card-title font-heading text-2xl">{{ character.name }}</h2>
            <div class="flex flex-wrap gap-1 my-2">
              <div v-for="tag in character.tags" :key="tag" class="badge badge-primary badge-outline">
                {{ tag }}
              </div>
            </div>
            <p class="text-slate-300">{{ character.description }}</p>
            
            <div v-if="character.attributes && Object.keys(character.attributes).length > 0" class="divider"></div>
            
            <div v-if="character.attributes && Object.keys(character.attributes).length > 0">
              <h3 class="font-heading text-lg font-bold">Key Information</h3>
              <div class="space-y-2 mt-2">
                <div v-for="(value, key) in character.attributes" :key="key" class="flex flex-col">
                  <span class="text-slate-400 text-sm">{{ formatLabel(key) }}</span>
                  <span>{{ value }}</span>
                </div>
              </div>
            </div>
            
            <div v-if="character.wiki_url" class="card-actions justify-end mt-4">
              <a :href="character.wiki_url" target="_blank" class="btn btn-outline btn-sm">
                View Wiki Source
              </a>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Main Content - Space for future mysteries -->
      <div class="md:col-span-2">
        <div class="card bg-base-300 shadow-xl">
          <div class="card-body">
            <h2 class="card-title font-heading text-2xl">Related Mysteries</h2>
            <p class="text-slate-400 text-center py-8">
              No mysteries created yet for this character.
              <br>
              <NuxtLink to="/mysteries/create" class="link link-primary">Create a mystery</NuxtLink> featuring {{ character.name }}.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const route = useRoute();
const client = useSupabaseClient();

const loading = ref(true);
const error = ref('');
const character = ref(null);

// Set meta title when character is loaded
watch(character, (newCharacter) => {
  if (newCharacter) {
    useHead({
      title: `${newCharacter.name} - Nerdology`
    });
  }
});

onMounted(async () => {
  try {
    // Fetch character data
    const { data: charData, error: charError } = await client
      .from('characters')
      .select('*')
      .eq('id', route.params.id)
      .single();
      
    if (charError) throw charError;
    
    // Fetch tags
    const { data: tags, error: tagError } = await client
      .from('character_tags')
      .select('tag')
      .eq('character_id', route.params.id);
      
    if (tagError) throw tagError;
    
    // Fetch attributes
    const { data: attrs, error: attrError } = await client
      .from('character_attributes')
      .select('key, value')
      .eq('character_id', route.params.id);
      
    if (attrError) throw attrError;
    
    // Combine all data
    character.value = {
      ...charData,
      tags: tags?.map(t => t.tag) || [],
      attributes: attrs?.reduce((acc, attr) => {
        acc[attr.key] = attr.value;
        return acc;
      }, {}) || {}
    };
  } catch (e) {
    console.error('Error fetching character:', e);
    error.value = e.message || 'Failed to load character';
  } finally {
    loading.value = false;
  }
});

const formatLabel = (key) => {
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};
</script>