<template>
  <div>
    <h1 class="text-3xl font-bold mb-8 font-heading">Add New Character</h1>
    <div class="card bg-base-300 shadow-xl max-w-4xl mx-auto">
      <div class="card-body">
        <!-- AI-Powered Character Extraction -->
        <div v-if="!extractedData" class="space-y-6">
          <div class="text-center mb-6">
            <h2 class="text-xl font-semibold mb-2">AI-Powered Character Extraction</h2>
            <p class="text-base-content/70">
              Enter a wiki URL and let Pica + Firecrawl AI create a comprehensive character profile with image
            </p>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Wiki URL</span>
            </label>
            <div class="flex gap-2">
              <input
                type="text"
                v-model="wikiUrl"
                placeholder="https://en.wikipedia.org/wiki/<PERSON>_Holmes"
                class="input input-bordered flex-1"
                :disabled="isProcessing"
                @keyup.enter="extractCharacter"
              />
              <button
                @click="extractCharacter"
                class="btn btn-primary"
                :disabled="!isValidUrl || isProcessing"
              >
                <span v-if="isProcessing" class="loading loading-spinner loading-sm mr-2"></span>
                {{ isProcessing ? 'Extracting...' : 'Extract' }}
              </button>
            </div>
            <div class="label">
              <span class="label-text-alt text-info">
                Supports Wikipedia, Fandom wikis, and other character pages
              </span>
            </div>
          </div>

          <div v-if="error" class="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ error }}</span>
          </div>

          <div class="divider">OR</div>

          <div class="text-center">
            <NuxtLink to="/characters/create" class="btn btn-outline">
              Use Manual Entry
            </NuxtLink>
          </div>
        </div>

        <!-- Extracted Character Data Review -->
        <div v-else class="space-y-6">
          <div class="alert alert-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Character data extracted successfully! Review and save below.</span>
          </div>

          <!-- Character Image Preview -->
          <div v-if="extractedData.image_url" class="flex justify-center">
            <div class="avatar">
              <div class="w-32 rounded-lg">
                <img :src="extractedData.image_url" :alt="extractedData.name" class="object-cover" />
              </div>
            </div>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Character Name</span>
            </label>
            <input
              type="text"
              v-model="extractedData.name"
              class="input input-bordered w-full"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Character Image URL</span>
            </label>
            <input
              type="text"
              v-model="extractedData.image_url"
              placeholder="https://example.com/character-image.jpg"
              class="input input-bordered w-full"
            />
            <div class="label">
              <span class="label-text-alt">Optional: URL to character image</span>
            </div>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Character Profile</span>
            </label>
            <textarea
              v-model="extractedData.description"
              class="textarea textarea-bordered h-48"
              placeholder="Comprehensive character profile including background, abilities, characteristics, and notable achievements..."
              maxlength="1200"
            ></textarea>
            <div class="label">
              <span class="label-text-alt">{{ extractedData.description?.length || 0 }}/1200 characters - AI-generated comprehensive character resume</span>
            </div>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Tags</span>
            </label>
            <input
              type="text"
              v-model="tagsInput"
              class="input input-bordered w-full"
            />
            <div class="label">
              <span class="label-text-alt">Comma separated tags</span>
            </div>
          </div>

          <div class="flex gap-2 mt-6">
            <button @click="saveCharacter" class="btn btn-primary flex-1" :disabled="isSaving">
              <span v-if="isSaving" class="loading loading-spinner loading-sm mr-2"></span>
              {{ isSaving ? 'Saving...' : 'Save Character' }}
            </button>
            <button @click="resetForm" class="btn btn-outline">
              Start Over
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { z } from 'zod';

definePageMeta({
  middleware: ['auth'] // Add auth middleware
});

const router = useRouter();
const client = useSupabaseClient();
const user = useSupabaseUser();

// State for AI extraction
const wikiUrl = ref('');
const isProcessing = ref(false);
const error = ref('');
const extractedData = ref(null);
const tagsInput = ref('');
const isSaving = ref(false);

// Computed property to validate URL
const isValidUrl = computed(() => {
  if (!wikiUrl.value) return false;
  try {
    new URL(wikiUrl.value);
    return wikiUrl.value.includes('wiki') ||
           wikiUrl.value.includes('fandom') ||
           wikiUrl.value.includes('wikipedia');
  } catch {
    return false;
  }
});

// Validation schema using Zod
const characterSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(50, 'Character profile must be at least 50 characters'),
  image_url: z.string().url('Invalid image URL').optional().or(z.literal('')),
  tags: z.array(z.string())
});

// Extract character data from wiki URL using Pica + Firecrawl
const extractCharacter = async () => {
  if (!isValidUrl.value) return;

  isProcessing.value = true;
  error.value = '';

  try {
    // Call the enhanced scrape API with character extraction
    const response = await fetch('/api/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: wikiUrl.value,
        extractCharacter: true // Flag to enable AI character extraction
      })
    });

    if (!response.ok) {
      throw new Error('Failed to extract character data');
    }

    const result = await response.json();

    if (result.success) {
      // Handle new JSON structure with character and scraped_data
      extractedData.value = result.data.character;
      tagsInput.value = result.data.character.tags.join(', ');

      // Log scraped data for debugging
      console.log('Scraped data:', result.data.scraped_data);
    } else {
      throw new Error(result.error || 'Failed to extract character data');
    }
  } catch (e) {
    console.error('Error extracting character:', e);
    error.value = e.message || 'Failed to extract character data. Please try again.';
  } finally {
    isProcessing.value = false;
  }
};

// Reset form to start over
const resetForm = () => {
  extractedData.value = null;
  wikiUrl.value = '';
  tagsInput.value = '';
  error.value = '';
};

const saveCharacter = async () => {
  if (!extractedData.value) return;

  try {
    // Process tags
    const tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag);

    // Validate data
    const characterData = {
      name: extractedData.value.name,
      description: extractedData.value.description,
      image_url: extractedData.value.image_url || '',
      tags
    };

    characterSchema.parse(characterData);

    isSaving.value = true;

    // Insert character
    const { data: newCharacter, error: charError } = await client
      .from('characters')
      .insert({
        name: characterData.name,
        description: characterData.description,
        image_url: characterData.image_url,
        wiki_url: wikiUrl.value, // Store the wiki URL for future mystery creation
        created_by: user.value.id
      })
      .select()
      .single();

    if (charError) throw charError;

    // Insert tags
    if (tags.length > 0) {
      const tagInserts = tags.map(tag => ({
        character_id: newCharacter.id,
        tag
      }));

      const { error: tagError } = await client
        .from('character_tags')
        .insert(tagInserts);

      if (tagError) throw tagError;
    }

    // Navigate to the character page
    router.push(`/characters/${newCharacter.id}`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(e => e.message).join('\n');
      alert(`Validation error: ${errorMessage}`);
    } else {
      console.error('Error saving character:', error);
      alert('Failed to save character. Please try again.');
    }
  } finally {
    isSaving.value = false;
  }
};
</script>
