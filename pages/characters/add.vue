<template>
  <div>
    <h1 class="text-3xl font-bold mb-8 font-heading">Add New Character</h1>
    <div class="card bg-base-300 shadow-xl max-w-4xl mx-auto">
      <div class="card-body">
        <NuxtLink to="/characters/create" class="btn btn-primary mb-4">
          Use Advanced Character Creator
        </NuxtLink>
        <p class="text-sm text-base-content/70 mb-6">
          The advanced creator includes wiki scraping and AI-assisted data extraction.
        </p>
        
        <!-- Simple character form -->
        <div class="space-y-6">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Character Name</span>
            </label>
            <input type="text" v-model="character.name" placeholder="Character Name" class="input input-bordered w-full" />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Description</span>
            </label>
            <textarea 
              v-model="character.description" 
              placeholder="Character description..." 
              class="textarea textarea-bordered h-32"
            ></textarea>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Tags (comma separated)</span>
            </label>
            <input 
              type="text" 
              v-model="tagsInput" 
              placeholder="detective, genius, mysterious" 
              class="input input-bordered w-full" 
            />
          </div>
          
          <div class="form-control mt-6">
            <button @click="saveCharacter" class="btn btn-primary" :disabled="isSaving">
              <span v-if="isSaving" class="loading loading-spinner loading-sm mr-2"></span>
              {{ isSaving ? 'Saving...' : 'Save Character' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { z } from 'zod';

definePageMeta({
  middleware: ['auth'] // Add auth middleware
});

const router = useRouter();
const client = useSupabaseClient();
const user = useSupabaseUser();

const character = ref({
  name: '',
  description: '',
  background: '',
  image_url: '',
  tags: []
});

const tagsInput = ref('');
const isSaving = ref(false);

// Validation schema using Zod
const characterSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  background: z.string().optional(),
  image_url: z.string().url('Invalid image URL').optional().or(z.literal('')),
  tags: z.array(z.string())
});

const saveCharacter = async () => {
  try {
    // Process tags
    character.value.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag);
    
    // Validate data
    const validatedData = characterSchema.parse(character.value);
    
    isSaving.value = true;
    
    // Insert character
    const { data: newCharacter, error: charError } = await client
      .from('characters')
      .insert({
        name: character.value.name,
        description: character.value.description,
        background: character.value.background || '',
        image_url: character.value.image_url || '',
        created_by: user.value.id
      })
      .select()
      .single();

    if (charError) throw charError;

    // Insert tags
    if (character.value.tags.length > 0) {
      const tagInserts = character.value.tags.map(tag => ({
        character_id: newCharacter.id,
        tag
      }));

      const { error: tagError } = await client
        .from('character_tags')
        .insert(tagInserts);

      if (tagError) throw tagError;
    }
    
    // Navigate to the character page
    router.push(`/characters/${newCharacter.id}`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(e => e.message).join('\n');
      alert(`Validation error: ${errorMessage}`);
    } else {
      console.error('Error saving character:', error);
      alert('Failed to save character. Please try again.');
    }
  } finally {
    isSaving.value = false;
  }
};
</script>
