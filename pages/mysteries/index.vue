<template>
  <div>
    <h1 class="text-3xl font-bold mb-8 font-heading">Mystery Investigations</h1>
    
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
      <div class="form-control w-full max-w-xs">
        <div class="input-group">
          <input type="text" v-model="searchQuery" placeholder="Search mysteries..." class="input input-bordered w-full" />
          <button class="btn btn-square">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </div>
      </div>
      <div class="flex gap-2">
        <select v-model="categoryFilter" class="select select-bordered">
          <option value="">All Categories</option>
          <option value="Murder Mystery">Murder Mystery</option>
          <option value="Disappearance">Disappearance</option>
          <option value="Theft">Theft</option>
          <option value="Conspiracy">Conspiracy</option>
          <option value="Code Breaking">Code Breaking</option>
          <option value="Other">Other</option>
        </select>
        <NuxtLink to="/mysteries/create" class="btn btn-primary">
          New Mystery
        </NuxtLink>
      </div>
    </div>
    
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-dots loading-lg text-primary"></span>
    </div>
    
    <div v-else-if="filteredMysteries.length === 0" class="text-center py-8">
      <p class="text-slate-400">No mysteries found. Try a different search or create a new mystery.</p>
    </div>
    
    <div v-else class="space-y-6">
      <div v-for="mystery in filteredMysteries" :key="mystery.id" class="card bg-base-300 shadow-xl hover:shadow-2xl transition-all duration-300">
        <div class="card-body">
          <div class="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
            <div class="flex-1">
              <h2 class="card-title text-2xl font-heading">{{ mystery.title }}</h2>
              <div class="flex flex-wrap items-center gap-2 my-2">
                <div class="badge badge-accent">{{ mystery.category }}</div>
                <div class="text-sm text-slate-400">{{ formatDate(mystery.created_at) }}</div>
                <div class="text-sm text-slate-400">by {{ mystery.created_by }}</div>
              </div>
              <p class="text-slate-300 mb-4">{{ mystery.description }}</p>
              
              <div class="flex flex-wrap gap-2 mb-4">
                <div v-for="(character, index) in mystery.related_characters" :key="index" class="flex items-center gap-1 bg-base-200 px-2 py-1 rounded-md">
                  <div class="avatar">
                    <div class="w-6 h-6 rounded-full">
                      <img :src="character.image_url" :alt="character.name" />
                    </div>
                  </div>
                  <span class="text-sm">{{ character.name }}</span>
                </div>
              </div>
            </div>
            
            <div class="w-full md:w-80 bg-base-200 p-4 rounded-lg">
              <h3 class="font-heading font-bold flex items-center gap-2 mb-2">
                <span>AI Analysis</span>
                <div v-if="mystery.ai_analysis" class="badge" :class="getConfidenceBadgeClass(mystery.ai_analysis.confidence)">
                  {{ getConfidenceLabel(mystery.ai_analysis.confidence) }}
                </div>
                <div v-else class="badge badge-outline">Pending</div>
              </h3>
              
              <div v-if="mystery.ai_analysis" class="text-sm text-slate-300">
                <p>{{ mystery.ai_analysis.summary }}</p>
                <div class="mt-2 text-xs text-slate-400">
                  Analysis based on {{ mystery.ai_analysis.sources_count }} sources
                </div>
              </div>
              <div v-else class="text-sm text-slate-400">
                <p>Analysis has not been generated yet.</p>
              </div>
              
              <div class="mt-4 flex justify-between items-center">
                <div>
                  <span class="text-xs text-slate-400">{{ mystery.comments_count }} comments</span>
                </div>
                <NuxtLink :to="`/mysteries/${mystery.id}`" class="btn btn-primary btn-sm">
                  View Details
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="flex justify-center mt-8">
      <div class="btn-group">
        <button 
          v-for="page in totalPages" 
          :key="page" 
          @click="currentPage = page"
          :class="[
            'btn', 
            currentPage === page ? 'btn-active' : ''
          ]"
        >
          {{ page }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// In a real implementation, these would be fetched from Supabase
const loading = ref(true);
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = 5;
const categoryFilter = ref('');

const mysteries = ref([]);

onMounted(async () => {
  // Simulate loading data
  setTimeout(() => {
    mysteries.value = [
      {
        id: 1,
        title: "Who Really Killed Professor Plum?",
        category: "Murder Mystery",
        description: "An investigation into the mysterious death of Professor Plum in the library. Several characters had motives, but the evidence is conflicting.",
        created_at: new Date(2023, 8, 15),
        created_by: "SherlockFan22",
        related_characters: [
          {
            name: "Professor Plum",
            image_url: "https://images.pexels.com/photos/5490235/pexels-photo-5490235.jpeg"
          },
          {
            name: "Miss Scarlet",
            image_url: "https://images.pexels.com/photos/7535060/pexels-photo-7535060.jpeg"
          }
        ],
        ai_analysis: {
          confidence: 0.85,
          summary: "Based on character patterns and room layout, Colonel Mustard is the most likely suspect with 85% confidence. The weapon was likely the candlestick, not the initially suspected lead pipe.",
          sources_count: 3
        },
        comments_count: 12
      },
      {
        id: 2,
        title: "The Vanishing of Eleanor Rigby",
        category: "Disappearance",
        description: "Eleanor Rigby disappeared without a trace. Was it voluntary or was foul play involved? Analyze the clues and character histories.",
        created_at: new Date(2023, 9, 22),
        created_by: "MysteryLover",
        related_characters: [
          {
            name: "Eleanor Rigby",
            image_url: "https://images.pexels.com/photos/7535060/pexels-photo-7535060.jpeg"
          }
        ],
        ai_analysis: {
          confidence: 0.62,
          summary: "Multiple scenarios are plausible. Evidence points to both voluntary disappearance (62% confidence) and foul play (38% confidence). More character background data needed.",
          sources_count: 2
        },
        comments_count: 8
      },
      {
        id: 3,
        title: "The Cipher in the Old Mansion",
        category: "Code Breaking",
        description: "A mysterious cipher was found in the walls of an abandoned mansion. What secrets does it reveal about the former residents?",
        created_at: new Date(2023, 10, 7),
        created_by: "CodeBreaker",
        related_characters: [
          {
            name: "Sherlock Holmes",
            image_url: "https://images.pexels.com/photos/7173056/pexels-photo-7173056.jpeg"
          }
        ],
        ai_analysis: {
          confidence: 0.93,
          summary: "The cipher appears to be a substitution cipher commonly used in the Victorian era. When decoded, it reveals the location of a hidden family fortune. High confidence in this interpretation based on historical context.",
          sources_count: 5
        },
        comments_count: 15
      },
      {
        id: 4,
        title: "The Case of the Missing Watch",
        category: "Theft",
        description: "A valuable family heirloom watch has disappeared from a locked room. How was it taken and by whom?",
        created_at: new Date(2023, 11, 3),
        created_by: "DetectiveNovice",
        related_characters: [
          {
            name: "Sherlock Holmes",
            image_url: "https://images.pexels.com/photos/7173056/pexels-photo-7173056.jpeg"
          },
          {
            name: "Professor Moriarty",
            image_url: "https://images.pexels.com/photos/7534294/pexels-photo-7534294.jpeg"
          }
        ],
        ai_analysis: null,
        comments_count: 3
      },
      {
        id: 5,
        title: "The Secret Society of Baker Street",
        category: "Conspiracy",
        description: "Evidence suggests that a secret society has been operating out of Baker Street for decades. What is their purpose and who are its members?",
        created_at: new Date(2023, 11, 20),
        created_by: "ConspiracyHunter",
        related_characters: [
          {
            name: "Sherlock Holmes",
            image_url: "https://images.pexels.com/photos/7173056/pexels-photo-7173056.jpeg"
          },
          {
            name: "Mycroft Holmes",
            image_url: "https://images.pexels.com/photos/7534294/pexels-photo-7534294.jpeg"
          }
        ],
        ai_analysis: {
          confidence: 0.45,
          summary: "Evidence is circumstantial and contradictory. Low confidence in any single theory. Further investigation needed into Mycroft Holmes' government connections.",
          sources_count: 4
        },
        comments_count: 22
      }
    ];
    loading.value = false;
  }, 1000);
});

const filteredMysteries = computed(() => {
  let filtered = mysteries.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(mystery => 
      mystery.title.toLowerCase().includes(query) || 
      mystery.description.toLowerCase().includes(query)
    );
  }
  
  if (categoryFilter.value) {
    filtered = filtered.filter(mystery => 
      mystery.category === categoryFilter.value
    );
  }
  
  // Pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return filtered.slice(start, end);
});

const totalPages = computed(() => {
  let filtered = mysteries.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(mystery => 
      mystery.title.toLowerCase().includes(query) || 
      mystery.description.toLowerCase().includes(query)
    );
  }
  
  if (categoryFilter.value) {
    filtered = filtered.filter(mystery => 
      mystery.category === categoryFilter.value
    );
  }
  
  return Math.ceil(filtered.length / itemsPerPage);
});

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getConfidenceBadgeClass = (confidence) => {
  if (confidence >= 0.8) return 'badge-success';
  if (confidence >= 0.6) return 'badge-warning';
  return 'badge-error';
};

const getConfidenceLabel = (confidence) => {
  if (confidence >= 0.8) return 'High';
  if (confidence >= 0.6) return 'Medium';
  return 'Low';
};

// Reset to page 1 when search query or category filter changes
watch([searchQuery, categoryFilter], () => {
  currentPage.value = 1;
});
</script>