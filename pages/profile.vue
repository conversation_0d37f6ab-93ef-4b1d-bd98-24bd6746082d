<template>
  <div>
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-dots loading-lg text-primary"></span>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <span>{{ error }}</span>
    </div>

    <div v-else class="max-w-4xl mx-auto">
      <!-- Email Verification Alert -->
      <div v-if="!user?.email_confirmed_at" class="alert alert-warning mb-8">
        <span>Please check your email to verify your account. Some features may be limited until verification is complete.</span>
        <button @click="resendVerification" class="btn btn-sm btn-ghost" :disabled="resending">
          {{ resending ? 'Sending...' : 'Resend Email' }}
        </button>
      </div>

      <!-- Profile Header -->
      <div class="card bg-base-300 shadow-xl mb-8">
        <div class="card-body">
          <div class="flex flex-col md:flex-row gap-6 items-start">
            <div class="avatar placeholder">
              <div class="bg-primary text-base-100 rounded-full w-24">
                <span class="text-3xl">{{ userInitials }}</span>
              </div>
            </div>
            
            <div class="flex-1">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Display Name</span>
                </label>
                <input 
                  type="text" 
                  v-model="profileData.display_name" 
                  class="input input-bordered" 
                  :disabled="saving"
                />
              </div>
              
              <div class="form-control mt-4">
                <label class="label">
                  <span class="label-text">Username</span>
                </label>
                <input 
                  type="text" 
                  v-model="profileData.username" 
                  class="input input-bordered" 
                  :disabled="saving"
                />
              </div>
              
              <div class="form-control mt-4">
                <label class="label">
                  <span class="label-text">Bio</span>
                </label>
                <textarea 
                  v-model="profileData.bio" 
                  class="textarea textarea-bordered h-24" 
                  :disabled="saving"
                ></textarea>
              </div>
              
              <div class="mt-6">
                <button 
                  @click="saveProfile" 
                  class="btn btn-primary" 
                  :disabled="saving || !hasChanges"
                >
                  <span v-if="saving" class="loading loading-spinner loading-sm mr-2"></span>
                  {{ saving ? 'Saving...' : 'Save Changes' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Section -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Created Characters -->
        <div class="card bg-base-300 shadow-xl">
          <div class="card-body">
            <h2 class="card-title font-heading">Your Characters</h2>
            <div v-if="userCharacters.length === 0" class="text-center py-4 text-base-content/70">
              You haven't created any characters yet.
            </div>
            <div v-else class="space-y-4">
              <div v-for="char in userCharacters" :key="char.id" class="flex items-center gap-4">
                <div class="avatar">
                  <div class="w-12 h-12 rounded-full">
                    <img :src="char.image_url || 'https://images.pexels.com/photos/7173056/pexels-photo-7173056.jpeg'" :alt="char.name">
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="font-bold">{{ char.name }}</h3>
                  <p class="text-sm text-base-content/70">Created {{ formatDate(char.created_at) }}</p>
                </div>
                <NuxtLink :to="`/characters/${char.id}`" class="btn btn-ghost btn-sm">View</NuxtLink>
              </div>
            </div>
          </div>
        </div>

        <!-- Created Mysteries -->
        <div class="card bg-base-300 shadow-xl">
          <div class="card-body">
            <h2 class="card-title font-heading">Your Mysteries</h2>
            <div v-if="userMysteries.length === 0" class="text-center py-4 text-base-content/70">
              You haven't created any mysteries yet.
            </div>
            <div v-else class="space-y-4">
              <div v-for="mystery in userMysteries" :key="mystery.id" class="flex items-center gap-4">
                <div class="flex-1">
                  <h3 class="font-bold">{{ mystery.title }}</h3>
                  <p class="text-sm text-base-content/70">Created {{ formatDate(mystery.created_at) }}</p>
                </div>
                <NuxtLink :to="`/mysteries/${mystery.id}`" class="btn btn-ghost btn-sm">View</NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const client = useSupabaseClient();
const user = useSupabaseUser();
const router = useRouter();

// Redirect if not logged in
if (!user.value) {
  router.push('/auth/login');
}

const loading = ref(true);
const saving = ref(false);
const resending = ref(false);
const error = ref('');

const profileData = ref({
  username: '',
  display_name: '',
  bio: ''
});

const originalProfile = ref(null);
const userCharacters = ref([]);
const userMysteries = ref([]);

// Computed property for user initials
const userInitials = computed(() => {
  if (profileData.value.display_name) {
    return profileData.value.display_name.charAt(0).toUpperCase();
  }
  if (profileData.value.username) {
    return profileData.value.username.charAt(0).toUpperCase();
  }
  return user.value?.email?.charAt(0).toUpperCase() || '?';
});

// Check if profile has changes
const hasChanges = computed(() => {
  if (!originalProfile.value) return false;
  return JSON.stringify(profileData.value) !== JSON.stringify(originalProfile.value);
});

// Resend verification email
async function resendVerification() {
  if (!user.value?.email) return;
  
  resending.value = true;
  
  try {
    const { error: resendError } = await client.auth.resend({
      type: 'signup',
      email: user.value.email
    });
    
    if (resendError) throw resendError;
    
    alert('Verification email sent! Please check your inbox.');
  } catch (e) {
    console.error('Error resending verification:', e);
    error.value = e.message;
  } finally {
    resending.value = false;
  }
}

// Initialize default profile
function initializeDefaultProfile() {
  const defaultProfile = {
    id: user.value.id,
    username: user.value.email?.split('@')[0] || '',
    display_name: user.value.email?.split('@')[0] || '',
    bio: ''
  };
  profileData.value = defaultProfile;
  originalProfile.value = { ...defaultProfile };
}

// Load user data
async function loadUserData() {
  try {
    // Fetch user profile
    const { data: profile, error: profileError } = await client
      .from('users')
      .select('*')
      .eq('id', user.value.id)
      .single();

    // If no profile exists, initialize with defaults
    if (profileError && profileError.code === 'PGRST116') {
      initializeDefaultProfile();
    } else if (profileError) {
      throw profileError;
    } else {
      profileData.value = profile;
      originalProfile.value = { ...profile };
    }

    // Fetch user's characters
    const { data: characters, error: charError } = await client
      .from('characters')
      .select('*')
      .eq('created_by', user.value.id)
      .order('created_at', { ascending: false });

    if (charError) throw charError;
    userCharacters.value = characters || [];

    // Fetch user's mysteries
    const { data: mysteries, error: mysteryError } = await client
      .from('mysteries')
      .select('*')
      .eq('created_by', user.value.id)
      .order('created_at', { ascending: false });

    if (mysteryError) throw mysteryError;
    userMysteries.value = mysteries || [];

  } catch (e) {
    console.error('Error loading user data:', e);
    error.value = e.message;
  } finally {
    loading.value = false;
  }
}

// Save profile changes
async function saveProfile() {
  if (!hasChanges.value) return;

  saving.value = true;
  error.value = '';

  try {
    const { error: updateError } = await client
      .from('users')
      .upsert({
        id: user.value.id,
        username: profileData.value.username,
        display_name: profileData.value.display_name,
        bio: profileData.value.bio
      });

    if (updateError) throw updateError;

    // Update original profile to reflect saved changes
    originalProfile.value = { ...profileData.value };
  } catch (e) {
    console.error('Error saving profile:', e);
    error.value = e.message;
  } finally {
    saving.value = false;
  }
}

// Format date helper
function formatDate(date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Load data when component mounts
onMounted(() => {
  if (user.value) {
    loadUserData();
  }
});
</script>