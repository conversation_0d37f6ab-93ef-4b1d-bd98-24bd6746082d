<template>
  <div>
    <section class="hero min-h-[70vh] relative overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-base-300">
        <div class="absolute inset-0 opacity-10">
          <div class="absolute inset-0 bg-[url('https://images.pexels.com/photos/5477777/pexels-photo-5477777.jpeg')] bg-cover bg-center"></div>
          <div class="absolute inset-0 bg-gradient-to-b from-base-300/50 to-base-300"></div>
        </div>
        <!-- Investigation Elements Overlay -->
        <div class="absolute inset-0 opacity-5">
          <div class="grid grid-cols-6 gap-4 p-8">
            <div v-for="i in 24" :key="i" class="aspect-square rounded-full border-2 border-current"></div>
          </div>
        </div>
      </div>
      
      <!-- Hero Content -->
      <div class="hero-content text-center relative z-10">
        <div class="max-w-4xl">
          <h1 class="text-4xl md:text-6xl font-bold mb-6 font-heading">
            <span class="text-base-content">Uncover the Truth</span>
            <br class="hidden md:block" />
            <span class="text-primary">Solve the Mystery</span>
          </h1>
          <p class="text-xl md:text-2xl mb-8 text-base-content/80">
            Analyze fictional characters, investigate mysteries, and collaborate with others using AI-powered insights.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <NuxtLink to="/characters" class="btn btn-primary btn-lg">
              Explore Characters
            </NuxtLink>
            <NuxtLink to="/mysteries" class="btn btn-outline btn-primary btn-lg">
              Investigate Mysteries
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <section class="py-16 bg-base-200">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold mb-12 text-center font-heading">How It Works</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div class="card-body">
              <div class="mb-4 text-center">
                <div class="w-16 h-16 rounded-full bg-primary mx-auto flex items-center justify-center">
                  <span class="text-2xl font-bold text-base-100">1</span>
                </div>
              </div>
              <h3 class="card-title font-heading justify-center">Upload Character Data</h3>
              <p class="text-center text-base-content/70">
                Submit wiki content about fictional characters and our AI will extract key information.
              </p>
            </div>
          </div>
          <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div class="card-body">
              <div class="mb-4 text-center">
                <div class="w-16 h-16 rounded-full bg-primary mx-auto flex items-center justify-center">
                  <span class="text-2xl font-bold text-base-100">2</span>
                </div>
              </div>
              <h3 class="card-title font-heading justify-center">Create Mystery Topics</h3>
              <p class="text-center text-base-content/70">
                Start discussions about specific mysteries related to characters and stories.
              </p>
            </div>
          </div>
          <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
            <div class="card-body">
              <div class="mb-4 text-center">
                <div class="w-16 h-16 rounded-full bg-primary mx-auto flex items-center justify-center">
                  <span class="text-2xl font-bold text-base-100">3</span>
                </div>
              </div>
              <h3 class="card-title font-heading justify-center">Analyze with AI</h3>
              <p class="text-center text-base-content/70">
                Get AI-powered insights with confidence scoring and reasoning based on available data.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="py-16">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold mb-8 text-center font-heading">Featured Mysteries</h2>
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-dots loading-lg text-primary"></span>
        </div>
        <div v-else-if="featuredMysteries.length === 0" class="text-center py-8">
          <p class="text-base-content/70">No mysteries yet. Be the first to create one!</p>
          <NuxtLink to="/mysteries/create" class="btn btn-primary mt-4">Create Mystery</NuxtLink>
        </div>
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="mystery in featuredMysteries" :key="mystery.id" 
               class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div class="card-body">
              <h3 class="card-title font-heading text-xl">{{ mystery.title }}</h3>
              <div class="flex items-center gap-2 mb-2">
                <div class="badge badge-primary">{{ mystery.category }}</div>
                <div class="text-sm text-base-content/70">{{ formatDate(mystery.created_at) }}</div>
              </div>
              <p class="text-base-content/80 line-clamp-3">{{ mystery.description }}</p>
              <div class="card-actions justify-end mt-4">
                <NuxtLink :to="`/mysteries/${mystery.id}`" class="btn btn-primary btn-sm">
                  View Details
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mt-8">
          <NuxtLink to="/mysteries" class="btn btn-outline btn-primary">View All Mysteries</NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
const loading = ref(true);
const featuredMysteries = ref([]);

onMounted(async () => {
  setTimeout(() => {
    featuredMysteries.value = [
      {
        id: 1,
        title: "Who Really Killed Professor Plum?",
        category: "Murder Mystery",
        description: "An investigation into the mysterious death of Professor Plum in the library. Several characters had motives, but the evidence is conflicting.",
        created_at: new Date(2023, 8, 15)
      },
      {
        id: 2,
        title: "The Vanishing of Eleanor Rigby",
        category: "Disappearance",
        description: "Eleanor Rigby disappeared without a trace. Was it voluntary or was foul play involved? Analyze the clues and character histories.",
        created_at: new Date(2023, 9, 22)
      },
      {
        id: 3,
        title: "The Cipher in the Old Mansion",
        category: "Code Breaking",
        description: "A mysterious cipher was found in the walls of an abandoned mansion. What secrets does it reveal about the former residents?",
        created_at: new Date(2023, 10, 7)
      }
    ];
    loading.value = false;
  }, 1000);
});

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
</script>