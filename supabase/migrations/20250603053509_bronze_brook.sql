/*
  # Test tables without <PERSON><PERSON> for character scraping

  1. New Tables
    - `test_characters` - Character information without RLS
    - `test_character_attributes` - Character attributes without RLS
    - `test_character_tags` - Character tags without RLS
*/

-- Test Characters table
CREATE TABLE IF NOT EXISTS test_characters (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  background TEXT,
  image_url TEXT,
  wiki_url TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Test Character attributes table
CREATE TABLE IF NOT EXISTS test_character_attributes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id UUID NOT NULL REFERENCES test_characters(id) ON DELETE CASCADE,
  key TEXT NOT NULL,
  value TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(character_id, key)
);

-- Test Character tags table
CREATE TABLE IF NOT EXISTS test_character_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id UUID NOT NULL REFERENCES test_characters(id) ON DELETE CASCADE,
  tag TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(character_id, tag)
);