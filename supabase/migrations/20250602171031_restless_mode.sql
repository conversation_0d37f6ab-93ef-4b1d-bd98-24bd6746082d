/*
  # Initial database schema for Mystery Minds

  1. New Tables
    - `users` - Extended user profile information
    - `characters` - Character information extracted from wiki content
    - `character_attributes` - Flexible attributes for characters
    - `mysteries` - Mystery investigation topics
    - `mystery_analyses` - AI analyses of mysteries
    - `mystery_comments` - User comments on mysteries
    - `evidence` - Evidence items for mysteries
    - `character_mysteries` - Junction table linking characters to mysteries

  2. Security
    - Enable RLS on all tables
    - Set up appropriate policies for each table
*/

-- Create extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  username TEXT UNIQUE,
  display_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Characters table
CREATE TABLE IF NOT EXISTS characters (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  background TEXT,
  image_url TEXT,
  wiki_url TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Character attributes table (for flexible attributes)
CREATE TABLE IF NOT EXISTS character_attributes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
  key TEXT NOT NULL,
  value TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(character_id, key)
);

-- Character tags table
CREATE TABLE IF NOT EXISTS character_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
  tag TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(character_id, tag)
);

-- Mysteries table
CREATE TABLE IF NOT EXISTS mysteries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  view_count INTEGER DEFAULT 0
);

-- Mystery analyses table
CREATE TABLE IF NOT EXISTS mystery_analyses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mystery_id UUID NOT NULL REFERENCES mysteries(id) ON DELETE CASCADE,
  summary TEXT,
  detailed_analysis TEXT,
  confidence NUMERIC(3,2) CHECK (confidence >= 0 AND confidence <= 1),
  evolution TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Analysis sources table
CREATE TABLE IF NOT EXISTS analysis_sources (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  analysis_id UUID NOT NULL REFERENCES mystery_analyses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Mystery comments table
CREATE TABLE IF NOT EXISTS mystery_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mystery_id UUID NOT NULL REFERENCES mysteries(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  content TEXT NOT NULL,
  evidence TEXT,
  likes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Evidence table
CREATE TABLE IF NOT EXISTS evidence (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mystery_id UUID NOT NULL REFERENCES mysteries(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  impact INTEGER, -- Percentage impact on analysis confidence
  added_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Character-Mystery junction table
CREATE TABLE IF NOT EXISTS character_mysteries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
  mystery_id UUID NOT NULL REFERENCES mysteries(id) ON DELETE CASCADE,
  role TEXT, -- e.g., "Suspect", "Witness", "Investigator"
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(character_id, mystery_id)
);

-- Timeline events table
CREATE TABLE IF NOT EXISTS timeline_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
  mystery_id UUID REFERENCES mysteries(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  event_date TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),
  CHECK (character_id IS NOT NULL OR mystery_id IS NOT NULL)
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE character_attributes ENABLE ROW LEVEL SECURITY;
ALTER TABLE character_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE mysteries ENABLE ROW LEVEL SECURITY;
ALTER TABLE mystery_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE mystery_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE evidence ENABLE ROW LEVEL SECURITY;
ALTER TABLE character_mysteries ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_events ENABLE ROW LEVEL SECURITY;

-- Users Policies
CREATE POLICY "Users can view all profiles"
  ON users FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update their own profile"
  ON users FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Characters Policies
CREATE POLICY "Characters are viewable by everyone"
  ON characters FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create characters"
  ON characters FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Users can update characters they created"
  ON characters FOR UPDATE
  TO authenticated
  USING (auth.uid() = created_by);

-- Character Attributes Policies
CREATE POLICY "Character attributes are viewable by everyone"
  ON character_attributes FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create character attributes"
  ON character_attributes FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Users can update character attributes they created"
  ON character_attributes FOR UPDATE
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM characters
    WHERE characters.id = character_attributes.character_id
    AND characters.created_by = auth.uid()
  ));

-- Character Tags Policies
CREATE POLICY "Character tags are viewable by everyone"
  ON character_tags FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create character tags"
  ON character_tags FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Mysteries Policies
CREATE POLICY "Mysteries are viewable by everyone"
  ON mysteries FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create mysteries"
  ON mysteries FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Users can update mysteries they created"
  ON mysteries FOR UPDATE
  TO authenticated
  USING (auth.uid() = created_by);

-- Mystery Analyses Policies
CREATE POLICY "Mystery analyses are viewable by everyone"
  ON mystery_analyses FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create mystery analyses"
  ON mystery_analyses FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Analysis Sources Policies
CREATE POLICY "Analysis sources are viewable by everyone"
  ON analysis_sources FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create analysis sources"
  ON analysis_sources FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Mystery Comments Policies
CREATE POLICY "Mystery comments are viewable by everyone"
  ON mystery_comments FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create mystery comments"
  ON mystery_comments FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments"
  ON mystery_comments FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Evidence Policies
CREATE POLICY "Evidence is viewable by everyone"
  ON evidence FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create evidence"
  ON evidence FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Users can update evidence they added"
  ON evidence FOR UPDATE
  TO authenticated
  USING (auth.uid() = added_by);

-- Character-Mystery Junction Policies
CREATE POLICY "Character-mystery connections are viewable by everyone"
  ON character_mysteries FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create character-mystery connections"
  ON character_mysteries FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Timeline Events Policies
CREATE POLICY "Timeline events are viewable by everyone"
  ON timeline_events FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create timeline events"
  ON timeline_events FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Add function to update timestamps
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = now();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for timestamp updates
CREATE TRIGGER update_users_timestamp
BEFORE UPDATE ON users
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_characters_timestamp
BEFORE UPDATE ON characters
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_mysteries_timestamp
BEFORE UPDATE ON mysteries
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_mystery_comments_timestamp
BEFORE UPDATE ON mystery_comments
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();