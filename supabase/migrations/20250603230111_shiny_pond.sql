/*
  # Fix User RLS Policies

  1. Changes
    - Enable RLS on users table
    - Add policy for inserting new user profiles
    - Add policy for users to update their own profiles
    - Add policy for viewing user profiles

  2. Security
    - Users can only insert their own profile (id must match auth.uid())
    - Users can only update their own profile
    - Everyone can view user profiles
*/

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can update their own profile" ON users;
DROP POLICY IF EXISTS "Users can view all profiles" ON users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON users;

-- Create insert policy
CREATE POLICY "Users can insert their own profile"
ON users
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = id);

-- Create update policy
CREATE POLICY "Users can update their own profile"
ON users
FOR UPDATE
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Create select policy
CREATE POLICY "Users can view all profiles"
ON users
FOR SELECT
TO public
USING (true);