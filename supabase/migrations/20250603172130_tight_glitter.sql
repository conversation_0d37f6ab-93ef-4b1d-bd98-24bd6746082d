/*
  # Update character visibility and access controls

  1. Changes
    - Remove <PERSON><PERSON> from test character tables
    - Update policies for character-related tables
    - Update policies for mystery-related tables

  2. Security
    - Allow public read access to all character and mystery data
    - Maintain authenticated-only access for creating/updating content
*/

-- Remove <PERSON><PERSON> from test tables (these are already public)
ALTER TABLE test_characters DISABLE ROW LEVEL SECURITY;
ALTER TABLE test_character_attributes DISABLE ROW LEVEL SECURITY;
ALTER TABLE test_character_tags DISABLE ROW LEVEL SECURITY;

-- Update character table policies
DROP POLICY IF EXISTS "Characters are viewable by everyone" ON characters;
CREATE POLICY "Characters are viewable by everyone"
  ON characters FOR SELECT
  USING (true);

-- Update character attributes policies
DROP POLICY IF EXISTS "Character attributes are viewable by everyone" ON character_attributes;
CREATE POLICY "Character attributes are viewable by everyone"
  ON character_attributes FOR SELECT
  USING (true);

-- Update character tags policies
DROP POLICY IF EXISTS "Character tags are viewable by everyone" ON character_tags;
CREATE POLICY "Character tags are viewable by everyone"
  ON character_tags FOR SELECT
  USING (true);

-- Update mystery policies
DROP POLICY IF EXISTS "Mysteries are viewable by everyone" ON mysteries;
CREATE POLICY "Mysteries are viewable by everyone"
  ON mysteries FOR SELECT
  USING (true);

-- Update mystery analyses policies
DROP POLICY IF EXISTS "Mystery analyses are viewable by everyone" ON mystery_analyses;
CREATE POLICY "Mystery analyses are viewable by everyone"
  ON mystery_analyses FOR SELECT
  USING (true);

-- Update mystery comments policies
DROP POLICY IF EXISTS "Mystery comments are viewable by everyone" ON mystery_comments;
CREATE POLICY "Mystery comments are viewable by everyone"
  ON mystery_comments FOR SELECT
  USING (true);

-- Update evidence policies
DROP POLICY IF EXISTS "Evidence is viewable by everyone" ON evidence;
CREATE POLICY "Evidence is viewable by everyone"
  ON evidence FOR SELECT
  USING (true);

-- Update character-mystery policies
DROP POLICY IF EXISTS "Character-mystery connections are viewable by everyone" ON character_mysteries;
CREATE POLICY "Character-mystery connections are viewable by everyone"
  ON character_mysteries FOR SELECT
  USING (true);

-- Update timeline events policies
DROP POLICY IF EXISTS "Timeline events are viewable by everyone" ON timeline_events;
CREATE POLICY "Timeline events are viewable by everyone"
  ON timeline_events FOR SELECT
  USING (true);