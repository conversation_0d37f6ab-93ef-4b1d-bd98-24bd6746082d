import { createClient } from "npm:@supabase/supabase-js@2";
import { load } from "npm:cheerio@1.0.0-rc.12";
import { encode as base64Encode } from "npm:base64-arraybuffer@1.0.2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

async function scrapeWikiContent(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    const html = await response.text();
    const $ = load(html);
    
    // Remove unwanted elements
    $('script').remove();
    $('style').remove();
    $('.mw-editsection').remove();
    $('.reference').remove();
    $('sup.reference').remove();
    $('.error').remove();
    
    // Get the main content
    const content = $('#mw-content-text').text() || $('.mw-parser-output').text();
    return content.trim();
  } catch (error) {
    console.error(`Error scraping wiki content from ${url}:`, error);
    return '';
  }
}

async function analyzeMystery(mysteryData: any, wikiContents: string[], previousAnalyses: any[] = []) {
  // Combine all wiki content for analysis
  const combinedContent = wikiContents.join('\n\n');
  
  // Calculate confidence based on amount of evidence and wiki content
  const evidenceCount = mysteryData.evidence?.length || 0;
  const wikiContentScore = wikiContents.reduce((score, content) => {
    return score + (content.length > 1000 ? 0.2 : 0.1);
  }, 0);
  
  const baseConfidence = 0.5 + 
    (Math.min(evidenceCount, 10) / 20) + // 0 to 0.5 based on evidence
    Math.min(wikiContentScore, 0.3); // 0 to 0.3 based on wiki content
  
  // Adjust confidence based on previous analyses
  let confidence = baseConfidence;
  if (previousAnalyses.length > 0) {
    confidence = Math.min(baseConfidence + (previousAnalyses.length * 0.05), 0.95);
  }
  
  // Generate summary based on mystery details and wiki content
  const summary = `Analysis of "${mysteryData.title}" incorporates ${wikiContents.length} character profiles and ${evidenceCount} pieces of evidence. ${
    confidence > 0.8 
      ? 'Strong correlations found between character backgrounds and current evidence.' 
      : 'Additional character information or evidence may be needed for a more definitive analysis.'
  }`;
  
  // Generate detailed analysis incorporating wiki content insights
  const detailedAnalysis = `This comprehensive analysis examines the relationship between characters, their established histories, and current evidence.\n\n${
    wikiContents.length > 0
      ? `Character background analysis reveals key patterns and potential motivations based on ${wikiContents.length} detailed character profiles.`
      : 'Limited character background information available for analysis.'
  }\n\n${
    evidenceCount > 0 
      ? `The ${evidenceCount} pieces of evidence provide ${confidence > 0.7 ? 'substantial' : 'some'} support for the primary theory.` 
      : 'No physical evidence has been recorded yet, limiting analysis to character backgrounds.'
  }`;
  
  // Generate evolution text if there are previous analyses
  let evolution = "";
  if (previousAnalyses.length > 0) {
    const lastAnalysis = previousAnalyses[previousAnalyses.length - 1];
    const confidenceDelta = confidence - (lastAnalysis.confidence || 0);
    
    evolution = `This analysis builds upon ${previousAnalyses.length} previous analyses and incorporates ${wikiContents.length} character profiles. ${
      confidenceDelta > 0 
        ? `Confidence has increased by ${(confidenceDelta * 100).toFixed(1)}% due to enhanced character background understanding and new evidence.` 
        : `Confidence remains similar to previous analysis as no significant new information has been incorporated.`
    }`;
  } else {
    evolution = `Initial analysis based on ${wikiContents.length} character profiles and current evidence.`;
  }
  
  // Generate sources based on available evidence and wiki content
  const sources = [];
  
  // Add evidence sources
  if (mysteryData.evidence) {
    mysteryData.evidence.forEach((evidence: any) => {
      sources.push({
        title: evidence.title,
        description: `${evidence.description.substring(0, 100)}${evidence.description.length > 100 ? '...' : ''}`
      });
    });
  }
  
  // Add character profile sources
  if (mysteryData.related_characters) {
    mysteryData.related_characters.forEach((character: any, index: number) => {
      if (wikiContents[index]) {
        sources.push({
          title: `${character.name} Profile`,
          description: `Character background analysis from wiki content (${Math.round(wikiContents[index].length / 1000)}KB of data)`
        });
      }
    });
  }
  
  return {
    confidence,
    summary,
    detailed_analysis: detailedAnalysis,
    evolution,
    sources: sources.length > 0 ? sources : [{ title: "Base Analysis", description: "Initial assessment with limited data" }]
  };
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }
  
  try {
    // Check if this is a POST request
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }
    
    const { mysteryId, userId } = await req.json();
    
    if (!mysteryId) {
      return new Response(JSON.stringify({ error: "Mystery ID is required" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }
    
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { persistSession: false } }
    );
    
    // Fetch mystery details
    const { data: mystery, error: mysteryError } = await supabaseClient
      .from('mysteries')
      .select(`
        *,
        evidence (*),
        character_mysteries (
          characters (
            id,
            name,
            wiki_url
          )
        )
      `)
      .eq('id', mysteryId)
      .single();
      
    if (mysteryError) throw mysteryError;
    
    // Fetch previous analyses
    const { data: previousAnalyses, error: analysesError } = await supabaseClient
      .from('mystery_analyses')
      .select('*')
      .eq('mystery_id', mysteryId)
      .order('created_at', { ascending: true });
      
    if (analysesError) throw analysesError;
    
    // Extract related characters
    const relatedCharacters = mystery.character_mysteries?.map((cm: any) => cm.characters) || [];
    
    // Scrape wiki content for all related characters
    const wikiContents = await Promise.all(
      relatedCharacters
        .filter((char: any) => char.wiki_url)
        .map((char: any) => scrapeWikiContent(char.wiki_url))
    );
    
    // Generate AI analysis
    const analysis = await analyzeMystery(mystery, wikiContents, previousAnalyses);
    
    // Save the analysis
    const { data: savedAnalysis, error: saveError } = await supabaseClient
      .from('mystery_analyses')
      .insert({
        mystery_id: mysteryId,
        created_by: userId,
        ...analysis
      })
      .select()
      .single();
      
    if (saveError) throw saveError;
    
    // Save analysis sources
    if (analysis.sources.length > 0) {
      const { error: sourcesError } = await supabaseClient
        .from('analysis_sources')
        .insert(
          analysis.sources.map(source => ({
            analysis_id: savedAnalysis.id,
            ...source
          }))
        );
        
      if (sourcesError) throw sourcesError;
    }
    
    // Return the analysis
    return new Response(JSON.stringify({
      success: true,
      data: savedAnalysis
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error) {
    console.error("Error in analyze-mystery function:", error);
    
    return new Response(JSON.stringify({ 
      error: "Internal server error",
      details: error.message 
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  }
});