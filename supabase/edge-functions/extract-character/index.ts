import { load } from "npm:cheerio@1.0.0-rc.12";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

// Extract character information from wiki content
function extractCharacterInfo(wikiContent: string, wikiUrl: string) {
  try {
    // Clean and normalize the content
    const cleanContent = wikiContent
      .replace(/\n+/g, '\n')
      .replace(/\s+/g, ' ')
      .trim();

    // Extract name (first significant line or heading)
    const name = cleanContent.split('\n')[0]?.split(' - ')[0]?.trim() || "Unknown Character";

    // Extract description (first paragraph after name)
    const descriptionMatch = cleanContent.match(/\n(.+?)(?=\n|$)/);
    const description = descriptionMatch ? descriptionMatch[1].trim() : "";

    // Extract background (remaining content)
    const backgroundStart = cleanContent.indexOf(description) + description.length;
    const background = cleanContent.slice(backgroundStart).trim();

    // Extract potential attributes
    const attributes: Record<string, string> = {};
    const commonAttributes = [
      'occupation', 'residence', 'age', 'species', 'gender', 
      'nationality', 'affiliation', 'first appearance'
    ];

    commonAttributes.forEach(attr => {
      const regex = new RegExp(`${attr}[:\\s]+([^\\n.]+)`, 'i');
      const match = cleanContent.match(regex);
      if (match) {
        attributes[attr.toLowerCase()] = match[1].trim();
      }
    });

    // Extract potential tags
    const tags = new Set<string>();
    const commonTags = [
      'hero', 'villain', 'detective', 'student', 'teacher', 'warrior',
      'scientist', 'magical', 'human', 'alien', 'robot', 'mutant'
    ];

    commonTags.forEach(tag => {
      if (cleanContent.toLowerCase().includes(tag.toLowerCase())) {
        tags.add(tag);
      }
    });

    // Add some basic category tags
    if (wikiUrl.includes('wikipedia')) tags.add('wikipedia');
    if (wikiUrl.includes('fandom')) tags.add('fandom');

    return {
      name,
      description: description || "No description available.",
      background: background || "No background information available.",
      attributes,
      tags: Array.from(tags),
      wiki_url: wikiUrl
    };
  } catch (error) {
    console.error("Error extracting character info:", error);
    return {
      name: "Unknown Character",
      description: "Failed to extract character information.",
      background: "",
      attributes: {},
      tags: [],
      wiki_url: wikiUrl
    };
  }
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }
  
  try {
    // Check if this is a POST request
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }
    
    // Get the request payload
    const { wikiContent, wikiUrl } = await req.json();
    
    if (!wikiContent || typeof wikiContent !== "string") {
      return new Response(JSON.stringify({ error: "Wiki content is required" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }
    
    // Extract character information from the wiki content
    const extractedInfo = extractCharacterInfo(wikiContent, wikiUrl || "");
    
    // Return the extracted character information
    return new Response(JSON.stringify({
      success: true,
      data: extractedInfo
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error) {
    console.error("Error in extract-character function:", error);
    
    return new Response(JSON.stringify({ 
      error: "Internal server error",
      details: error.message 
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  }
});