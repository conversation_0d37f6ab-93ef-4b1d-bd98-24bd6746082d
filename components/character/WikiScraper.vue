<template>
  <div class="card bg-base-300 shadow-xl max-w-4xl mx-auto">
    <div class="card-body">
      <div class="form-control">
        <label class="label">
          <span class="label-text">Wiki URL</span>
        </label>
        <div class="flex gap-2">
          <input 
            type="text" 
            v-model="wikiUrl" 
            placeholder="https://wikipedia.org/wiki/Character_Name" 
            class="input input-bordered flex-1" 
            :disabled="loading || saving"
          />
          <button 
            @click="scrapeWiki" 
            class="btn btn-primary" 
            :disabled="!isValidUrl || loading || saving"
          >
            <span v-if="loading" class="loading loading-spinner loading-sm mr-2"></span>
            {{ loading ? 'Scraping...' : 'Scrape' }}
          </button>
        </div>
      </div>

      <div v-if="error" class="alert alert-error mt-4">
        <span>{{ error }}</span>
      </div>

      <div v-if="extractedData" class="mt-6 space-y-4">
        <h3 class="text-xl font-bold font-heading">Extracted Character Data</h3>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Name</span>
          </label>
          <input type="text" v-model="extractedData.name" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Description</span>
          </label>
          <textarea v-model="extractedData.description" class="textarea textarea-bordered h-24"></textarea>
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Background</span>
          </label>
          <textarea v-model="extractedData.background" class="textarea textarea-bordered h-32"></textarea>
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Image URL</span>
          </label>
          <input type="text" v-model="extractedData.image_url" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Tags (comma separated)</span>
          </label>
          <input type="text" v-model="tagsInput" class="input input-bordered" />
        </div>
        
        <div class="form-control mt-6">
          <button @click="saveCharacter" class="btn btn-primary" :disabled="saving || loading">
            <span v-if="saving" class="loading loading-spinner loading-sm mr-2"></span>
            {{ saving ? 'Saving...' : 'Save Character' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useSupabaseClient, useRouter } from '#imports';
import * as cheerio from 'cheerio';

const client = useSupabaseClient();
const router = useRouter();

const wikiUrl = ref('');
const loading = ref(false);
const saving = ref(false);
const error = ref('');
const extractedData = ref<any>(null);
const tagsInput = ref('');

const isValidUrl = computed(() => {
  try {
    new URL(wikiUrl.value);
    return wikiUrl.value.includes('wiki') || wikiUrl.value.includes('fandom');
  } catch {
    return false;
  }
});

function extractCharacterInfo(html: string, url: string) {
  const $ = cheerio.load(html);
  
  // Remove unwanted elements
  $('script').remove();
  $('style').remove();
  $('.mw-editsection').remove();
  $('.reference').remove();
  $('sup.reference').remove();
  $('.error').remove();
  
  // Get main content
  const content = $('#mw-content-text').text() || $('.mw-parser-output').text();
  const cleanContent = content
    .replace(/\n+/g, '\n')
    .replace(/\s+/g, ' ')
    .trim();

  // Extract name (first significant line or heading)
  const name = $('.firstHeading').text() || 
    $('h1').first().text() ||
    url.split('/').pop()?.replace(/_/g, ' ') || 
    "Unknown Character";

  // Extract description (first paragraph)
  const description = $('#mw-content-text p').first().text().trim() ||
    $('.mw-parser-output p').first().text().trim() ||
    "No description available.";

  // Extract background (subsequent paragraphs)
  const background = $('#mw-content-text p').slice(1, 5).text().trim() ||
    $('.mw-parser-output p').slice(1, 5).text().trim() ||
    "No background information available.";

  // Extract image URL
  const imageUrl = $('.infobox img').first().attr('src') ||
    $('#mw-content-text img').first().attr('src') ||
    '';

  // Extract potential attributes
  const attributes: Record<string, string> = {};
  $('.infobox tr').each((_, elem) => {
    const label = $(elem).find('th').text().trim().toLowerCase();
    const value = $(elem).find('td').text().trim();
    if (label && value) {
      attributes[label] = value;
    }
  });

  // Extract potential tags
  const tags = new Set<string>();
  const commonTags = [
    'hero', 'villain', 'detective', 'student', 'teacher', 'warrior',
    'scientist', 'magical', 'human', 'alien', 'robot', 'mutant'
  ];

  commonTags.forEach(tag => {
    if (cleanContent.toLowerCase().includes(tag.toLowerCase())) {
      tags.add(tag);
    }
  });

  // Add source tag
  if (url.includes('wikipedia')) tags.add('wikipedia');
  if (url.includes('fandom')) tags.add('fandom');

  return {
    name,
    description,
    background,
    image_url: imageUrl ? new URL(imageUrl, url).href : '',
    attributes,
    tags: Array.from(tags)
  };
}

async function scrapeWiki() {
  if (!isValidUrl.value) return;
  
  loading.value = true;
  error.value = '';
  
  try {
    const response = await fetch('/api/scrape', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: wikiUrl.value })
    });

    if (!response.ok) throw new Error('Failed to fetch wiki content');
    
    const { data } = await response.json();
    
    // Extract character info locally
    const extracted = extractCharacterInfo(data.html, wikiUrl.value);
    
    extractedData.value = extracted;
    tagsInput.value = extracted.tags.join(', ');
  } catch (e) {
    console.error('Error scraping wiki:', e);
    error.value = e.message || 'Failed to extract character data';
  } finally {
    loading.value = false;
  }
}

async function saveCharacter() {
  if (!extractedData.value) return;
  
  saving.value = true;
  error.value = '';
  
  try {
    // Insert character
    const { data: character, error: charError } = await client
      .from('test_characters')
      .insert({
        name: extractedData.value.name,
        description: extractedData.value.description,
        background: extractedData.value.background,
        image_url: extractedData.value.image_url,
        wiki_url: wikiUrl.value
      })
      .select()
      .single();

    if (charError) throw charError;

    // Insert attributes
    if (extractedData.value.attributes) {
      const attrInserts = Object.entries(extractedData.value.attributes).map(([key, value]) => ({
        character_id: character.id,
        key,
        value
      }));

      const { error: attrError } = await client
        .from('test_character_attributes')
        .insert(attrInserts);

      if (attrError) throw attrError;
    }

    // Insert tags
    const tags = tagsInput.value.split(',').map(t => t.trim()).filter(t => t);
    if (tags.length > 0) {
      const tagInserts = tags.map(tag => ({
        character_id: character.id,
        tag
      }));

      const { error: tagError } = await client
        .from('test_character_tags')
        .insert(tagInserts);

      if (tagError) throw tagError;
    }

    // Navigate to the character page
    router.push(`/characters/${character.id}`);
  } catch (e) {
    console.error('Error saving character:', e);
    error.value = e.message || 'Failed to save character';
  } finally {
    saving.value = false;
  }
}
</script>