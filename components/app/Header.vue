<template>
  <header class="bg-base-200 shadow-md">
    <div class="container mx-auto px-4">
      <div class="navbar min-h-16">
        <div class="navbar-start">
          <div class="dropdown">
            <label tabindex="0" class="btn btn-ghost lg:hidden">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
              </svg>
            </label>
            <ul tabindex="0" class="menu menu-compact dropdown-content mt-3 p-2 shadow bg-base-200 rounded-box w-52">
              <li><NuxtLink to="/">Home</NuxtLink></li>
              <li><NuxtLink to="/characters">Characters</NuxtLink></li>
              <li><NuxtLink to="/characters/add">Add Character</NuxtLink></li>
              <li><NuxtLink to="/mysteries">Mysteries</NuxtLink></li>
              <li><NuxtLink to="/about">About</NuxtLink></li>
            </ul>
          </div>
          <NuxtLink to="/" class="flex items-center">
            <span class="font-heading text-xl md:text-2xl font-bold text-primary">nerd<span class="text-accent">dology</span></span>
          </NuxtLink>
        </div>
        <div class="navbar-center hidden lg:flex">
          <ul class="menu menu-horizontal px-1">
            <li><NuxtLink to="/" class="font-medium">Home</NuxtLink></li>
            <li><NuxtLink to="/characters" class="font-medium">Characters</NuxtLink></li>
            <li><NuxtLink to="/characters/add" class="font-medium">Add Character</NuxtLink></li>
            <li><NuxtLink to="/mysteries" class="font-medium">Mysteries</NuxtLink></li>
            <li><NuxtLink to="/about" class="font-medium">About</NuxtLink></li>
          </ul>
        </div>
        <div class="navbar-end flex items-center gap-2">
          <div v-if="user" class="dropdown dropdown-end">
            <label tabindex="0" class="btn btn-ghost btn-circle avatar">
              <div class="w-10 rounded-full bg-primary flex items-center justify-center">
                <span class="text-base-100 font-bold">{{ user.email?.charAt(0).toUpperCase() }}</span>
              </div>
            </label>
            <ul tabindex="0" class="menu menu-compact dropdown-content mt-3 p-2 shadow bg-base-200 rounded-box w-52">
              <li><NuxtLink to="/profile">Profile</NuxtLink></li>
              <li><a @click="handleLogout">Logout</a></li>
            </ul>
          </div>
          <NuxtLink v-else to="/auth/login" class="btn btn-primary btn-sm">Sign In</NuxtLink>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
const user = useSupabaseUser();
const client = useSupabaseClient();
const router = useRouter();

const handleLogout = async () => {
  await client.auth.signOut();
  router.push('/');
};
</script>