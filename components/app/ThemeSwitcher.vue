<template>
  <button
    @click="toggleTheme"
    class="btn btn-ghost btn-circle"
    :title="currentTheme === 'abyss' ? 'Switch to light mode' : 'Switch to dark mode'"
  >
    <iconify-icon v-show="currentTheme === 'abyss'" icon="solar:sun-bold" width="24" height="24"></iconify-icon>
    <iconify-icon v-show="currentTheme !== 'abyss'" icon="solar:moon-bold" width="24" height="24"></iconify-icon>
  </button>
</template>

<script setup>
import "iconify-icon";

const currentTheme = ref('abyss');

// Watch for system theme changes
const systemDarkMode = window?.matchMedia('(prefers-color-scheme: dark)');

const updateTheme = (theme: string) => {
  currentTheme.value = theme;
  document.documentElement.setAttribute('data-theme', theme);
  localStorage.setItem('theme', theme);
};

const handleSystemThemeChange = (e: MediaQueryListEvent | MediaQueryList) => {
  const savedTheme = localStorage.getItem('theme');
  if (!savedTheme) {
    updateTheme(e.matches ? 'abyss' : 'silk');
  }
};

onMounted(() => {
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) {
    updateTheme(savedTheme);
  } else {
    handleSystemThemeChange(systemDarkMode);
  }
  
  systemDarkMode.addEventListener('change', handleSystemThemeChange);
});

onUnmounted(() => {
  systemDarkMode.removeEventListener('change', handleSystemThemeChange);
});

const toggleTheme = () => {
  updateTheme(currentTheme.value === 'abyss' ? 'silk' : 'abyss');
};
</script>