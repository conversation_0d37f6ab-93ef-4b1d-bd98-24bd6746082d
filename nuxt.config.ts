import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: false },
  modules: [
    '@nuxtjs/supabase',
    '@pinia/nuxt'
  ],
  vite: {
    plugins: [tailwindcss()],
  },
  runtimeConfig: {
    // Private keys (only available on server-side)
    firecrawlApiKey: process.env.FIRECRAWL_API_KEY || '',
    openaiApiKey: process.env.OPENAI_API_KEY || process.env.AI_API_KEY || '',
    picaSecretKey: process.env.PICA_SECRET_KEY || '',
    picaFirecrawlConnectionKey: process.env.PICA_FIRECRAWL_CONNECTION_KEY || '',
    picaOpenaiConnectionKey: process.env.PICA_OPENAI_CONNECTION_KEY || '',
    public: {
      // Public keys (available on client-side)
    }
  },
  supabase: {
    redirectOptions: {
      login: '/auth/login',
      callback: '/auth/confirm',
      exclude: [
        '/',
        '/about',
        '/characters',
        '/characters/[id]',
        '/mysteries',
        '/mysteries/[id]'
      ]
    }
  },
  css: ['~/assets/css/main.css'],
  app: {
    head: {
      title: 'nerdology',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'AI-Powered Mystery Investigation Platform' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/nerdology.svg' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Source+Sans+Pro:wght@300;400;600&display=swap' }
      ]
    }
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag) => tag === "iconify-icon",
    }
  },
})