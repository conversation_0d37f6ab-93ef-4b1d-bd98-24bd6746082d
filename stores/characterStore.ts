import { defineStore } from 'pinia';
import { Character } from '~/models/Character';

export const useCharacterStore = defineStore('characters', () => {
  const characters = ref<Character[]>([]);
  const currentCharacter = ref<Character | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  const { getCharacters, getCharacter, createCharacter, extractCharacterData } = useCharacters();
  
  // Load all characters
  async function fetchCharacters() {
    loading.value = true;
    error.value = null;
    
    try {
      characters.value = await getCharacters();
    } catch (err) {
      console.error('Error fetching characters:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch characters';
    } finally {
      loading.value = false;
    }
  }
  
  // Load a single character
  async function fetchCharacter(id: string) {
    loading.value = true;
    error.value = null;
    
    try {
      currentCharacter.value = await getCharacter(id);
    } catch (err) {
      console.error('Error fetching character:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch character';
    } finally {
      loading.value = false;
    }
  }
  
  // Add a new character
  async function add<PERSON><PERSON>cter(character: Character) {
    loading.value = true;
    error.value = null;
    
    try {
      const id = await createCharacter(character);
      return id;
    } catch (err) {
      console.error('Error creating character:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create character';
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Extract character data from wiki content
  async function extractFromWiki(wikiContent: string, wikiUrl: string = '') {
    loading.value = true;
    error.value = null;
    
    try {
      const extractedData = await extractCharacterData(wikiContent, wikiUrl);
      return extractedData;
    } catch (err) {
      console.error('Error extracting character data:', err);
      error.value = err instanceof Error ? err.message : 'Failed to extract character data';
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  return {
    characters,
    currentCharacter,
    loading,
    error,
    fetchCharacters,
    fetchCharacter,
    addCharacter,
    extractFromWiki
  };
});