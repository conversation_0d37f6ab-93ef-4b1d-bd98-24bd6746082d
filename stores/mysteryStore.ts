import { defineStore } from 'pinia';
import { Mystery, Comment, Evidence } from '~/models/Mystery';

export const useMysteryStore = defineStore('mysteries', () => {
  const mysteries = ref<Mystery[]>([]);
  const currentMystery = ref<Mystery | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  const { getMysteries, getMystery, createMystery, runAnalysis, addComment, addEvidence } = useMysteries();
  
  // Load all mysteries
  async function fetchMysteries() {
    loading.value = true;
    error.value = null;
    
    try {
      mysteries.value = await getMysteries();
    } catch (err) {
      console.error('Error fetching mysteries:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch mysteries';
    } finally {
      loading.value = false;
    }
  }
  
  // Load a single mystery
  async function fetchMystery(id: string) {
    loading.value = true;
    error.value = null;
    
    try {
      currentMystery.value = await getMystery(id);
    } catch (err) {
      console.error('Error fetching mystery:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch mystery';
    } finally {
      loading.value = false;
    }
  }
  
  // Add a new mystery
  async function addMystery(mystery: Mystery, relatedCharacters: { id: string, role: string }[] = []) {
    loading.value = true;
    error.value = null;
    
    try {
      const id = await createMystery(mystery, relatedCharacters);
      return id;
    } catch (err) {
      console.error('Error creating mystery:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create mystery';
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Run AI analysis on the current mystery
  async function analyzeCurrentMystery() {
    if (!currentMystery.value) {
      error.value = 'No mystery selected';
      return null;
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const analysis = await runAnalysis(currentMystery.value.id!);
      
      // Update the current mystery with the new analysis
      if (currentMystery.value.analyses) {
        currentMystery.value.analyses.push({
          ...analysis,
          timestamp: new Date()
        });
      } else {
        currentMystery.value.analyses = [{
          ...analysis,
          timestamp: new Date()
        }];
      }
      
      return analysis;
    } catch (err) {
      console.error('Error analyzing mystery:', err);
      error.value = err instanceof Error ? err.message : 'Failed to analyze mystery';
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  // Add a comment to the current mystery
  async function commentOnMystery(comment: Partial<Comment>) {
    if (!currentMystery.value) {
      error.value = 'No mystery selected';
      return null;
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const newComment = await addComment({
        mystery_id: currentMystery.value.id!,
        content: comment.content!,
        evidence: comment.evidence,
      } as Comment);
      
      // Add the new comment to the current mystery
      if (currentMystery.value.comments) {
        currentMystery.value.comments.unshift({
          ...newComment,
          user_name: 'You', // This would normally come from the user profile
          timestamp: new Date()
        });
      }
      
      return newComment;
    } catch (err) {
      console.error('Error adding comment:', err);
      error.value = err instanceof Error ? err.message : 'Failed to add comment';
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  // Add evidence to the current mystery
  async function addEvidenceToMystery(evidence: Partial<Evidence>) {
    if (!currentMystery.value) {
      error.value = 'No mystery selected';
      return null;
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const newEvidence = await addEvidence({
        mystery_id: currentMystery.value.id!,
        title: evidence.title!,
        description: evidence.description!,
        impact: evidence.impact
      } as Evidence);
      
      // Add the new evidence to the current mystery
      if (currentMystery.value.evidence) {
        currentMystery.value.evidence.push({
          ...newEvidence,
          added_by: 'You' // This would normally come from the user profile
        });
      }
      
      return newEvidence;
    } catch (err) {
      console.error('Error adding evidence:', err);
      error.value = err instanceof Error ? err.message : 'Failed to add evidence';
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  return {
    mysteries,
    currentMystery,
    loading,
    error,
    fetchMysteries,
    fetchMystery,
    addMystery,
    analyzeCurrentMystery,
    commentOnMystery,
    addEvidenceToMystery
  };
});