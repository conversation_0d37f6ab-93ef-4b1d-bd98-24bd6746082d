import { Mystery, Analysis, Comment, Evidence } from '~/models/Mystery';

export const useMysteries = () => {
  const client = useSupabaseClient();
  const user = useSupabaseUser();
  
  // Get all mysteries
  async function getMysteries() {
    const { data, error } = await client
      .from('mysteries')
      .select(`
        *,
        users (username, display_name)
      `)
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    
    // Get related characters for each mystery
    const mysteriesWithCharacters = await Promise.all(
      data.map(async (mystery) => {
        const { data: relatedChars, error: relatedError } = await client
          .from('character_mysteries')
          .select(`
            role,
            characters (id, name, image_url)
          `)
          .eq('mystery_id', mystery.id);
          
        if (relatedError) throw relatedError;
        
        // Get latest analysis for each mystery
        const { data: analyses, error: analysesError } = await client
          .from('mystery_analyses')
          .select('*')
          .eq('mystery_id', mystery.id)
          .order('created_at', { ascending: false })
          .limit(1);
          
        if (analysesError) throw analysesError;
        
        // Get comment count
        const { count, error: countError } = await client
          .from('mystery_comments')
          .select('*', { count: 'exact', head: true })
          .eq('mystery_id', mystery.id);
          
        if (countError) throw countError;
        
        return {
          ...mystery,
          created_by: mystery.users?.display_name || mystery.users?.username || 'Unknown',
          related_characters: relatedChars?.map(rc => ({
            ...rc.characters,
            role: rc.role
          })) || [],
          ai_analysis: analyses?.length > 0 ? analyses[0] : null,
          comments_count: count || 0
        };
      })
    );
    
    return mysteriesWithCharacters;
  }
  
  // Get a single mystery by ID
  async function getMystery(id: string) {
    const { data, error } = await client
      .from('mysteries')
      .select(`
        *,
        users (username, display_name)
      `)
      .eq('id', id)
      .single();
      
    if (error) throw error;
    
    // Update view count
    const { error: updateError } = await client
      .from('mysteries')
      .update({ view_count: (data.view_count || 0) + 1 })
      .eq('id', id);
      
    if (updateError) console.error('Failed to update view count:', updateError);
    
    // Get related characters
    const { data: relatedChars, error: relatedError } = await client
      .from('character_mysteries')
      .select(`
        role,
        characters (id, name, image_url)
      `)
      .eq('mystery_id', id);
      
    if (relatedError) throw relatedError;
    
    // Get all analyses for this mystery
    const { data: analyses, error: analysesError } = await client
      .from('mystery_analyses')
      .select(`
        *,
        users (username, display_name)
      `)
      .eq('mystery_id', id)
      .order('created_at', { ascending: true });
      
    if (analysesError) throw analysesError;
    
    // Get sources for each analysis
    const analysesWithSources = await Promise.all(
      analyses.map(async (analysis) => {
        const { data: sources, error: sourcesError } = await client
          .from('analysis_sources')
          .select('*')
          .eq('analysis_id', analysis.id);
          
        if (sourcesError) throw sourcesError;
        
        return {
          ...analysis,
          created_by: analysis.users?.display_name || analysis.users?.username || 'System',
          sources: sources || [],
          timestamp: analysis.created_at
        };
      })
    );
    
    // Get comments
    const { data: comments, error: commentsError } = await client
      .from('mystery_comments')
      .select(`
        *,
        users (username, display_name)
      `)
      .eq('mystery_id', id)
      .order('created_at', { ascending: false });
      
    if (commentsError) throw commentsError;
    
    const formattedComments = comments.map(comment => ({
      ...comment,
      user_name: comment.users?.display_name || comment.users?.username || 'Anonymous',
      timestamp: comment.created_at
    }));
    
    // Get evidence
    const { data: evidence, error: evidenceError } = await client
      .from('evidence')
      .select(`
        *,
        users (username, display_name)
      `)
      .eq('mystery_id', id);
      
    if (evidenceError) throw evidenceError;
    
    const formattedEvidence = evidence.map(item => ({
      ...item,
      added_by: item.users?.display_name || item.users?.username || 'Anonymous'
    }));
    
    return {
      ...data,
      created_by: data.users?.display_name || data.users?.username || 'Unknown',
      related_characters: relatedChars?.map(rc => ({
        ...rc.characters,
        role: rc.role
      })) || [],
      analyses: analysesWithSources,
      comments: formattedComments,
      evidence: formattedEvidence
    };
  }
  
  // Create a new mystery
  async function createMystery(mystery: Mystery, relatedCharacters: { id: string, role: string }[] = []) {
    if (!user.value) throw new Error('You must be logged in to create a mystery');
    
    // First, create the mystery
    const { data, error } = await client
      .from('mysteries')
      .insert({
        title: mystery.title,
        description: mystery.description,
        category: mystery.category,
        created_by: user.value.id
      })
      .select()
      .single();
      
    if (error) throw error;
    
    const mysteryId = data.id;
    
    // Add related characters if they exist
    if (relatedCharacters.length > 0) {
      const charInserts = relatedCharacters.map(char => ({
        mystery_id: mysteryId,
        character_id: char.id,
        role: char.role
      }));
      
      const { error: charError } = await client
        .from('character_mysteries')
        .insert(charInserts);
        
      if (charError) throw charError;
    }
    
    return mysteryId;
  }
  
  // Run AI analysis on a mystery
  async function runAnalysis(mysteryId: string) {
    if (!user.value) throw new Error('You must be logged in to run an analysis');
    
    // This is where we would call an AI service to analyze the mystery
    // For now, we'll simulate the process with a timeout
    
    return new Promise<Analysis>((resolve) => {
      setTimeout(async () => {
        // Get previous analyses to determine evolution
        const { data: prevAnalyses, error: prevError } = await client
          .from('mystery_analyses')
          .select('*')
          .eq('mystery_id', mysteryId)
          .order('created_at', { ascending: false });
          
        if (prevError) throw prevError;
        
        // Create a mock analysis
        const analysis = {
          mystery_id: mysteryId,
          summary: 'This is an AI-generated analysis based on the available evidence and character information.',
          detailed_analysis: 'The detailed analysis examines multiple perspectives and evidence sources. Based on the patterns observed, we can identify key relationships and potential motives.\n\nThe chronological sequence of events suggests a deliberate and planned action rather than a spontaneous occurrence.',
          confidence: Math.random() * 0.4 + 0.6, // Random confidence between 0.6 and 1.0
          evolution: prevAnalyses?.length > 0 
            ? 'This analysis builds upon previous findings with new evidence incorporated.' 
            : 'This is the initial analysis based on current evidence.',
          created_by: user.value.id,
          sources: [
            { 
              title: 'Character Profiles', 
              description: 'Analysis of character traits, histories, and motivations.' 
            },
            { 
              title: 'Evidence Items', 
              description: 'Examination of physical and circumstantial evidence.' 
            }
          ]
        };
        
        // In a real implementation, we would save this to the database
        const { data, error } = await client
          .from('mystery_analyses')
          .insert({
            mystery_id: analysis.mystery_id,
            summary: analysis.summary,
            detailed_analysis: analysis.detailed_analysis,
            confidence: analysis.confidence,
            evolution: analysis.evolution,
            created_by: analysis.created_by
          })
          .select()
          .single();
          
        if (error) throw error;
        
        // Add sources
        if (analysis.sources && analysis.sources.length > 0) {
          const sourceInserts = analysis.sources.map(source => ({
            analysis_id: data.id,
            title: source.title,
            description: source.description
          }));
          
          const { error: sourceError } = await client
            .from('analysis_sources')
            .insert(sourceInserts);
            
          if (sourceError) throw sourceError;
        }
        
        // Return the created analysis
        resolve({
          ...analysis,
          id: data.id,
          created_at: new Date()
        });
      }, 3000);
    });
  }
  
  // Add a comment to a mystery
  async function addComment(comment: Comment) {
    if (!user.value) throw new Error('You must be logged in to add a comment');
    
    const { data, error } = await client
      .from('mystery_comments')
      .insert({
        mystery_id: comment.mystery_id,
        user_id: user.value.id,
        content: comment.content,
        evidence: comment.evidence
      })
      .select()
      .single();
      
    if (error) throw error;
    
    return data;
  }
  
  // Add evidence to a mystery
  async function addEvidence(evidence: Evidence) {
    if (!user.value) throw new Error('You must be logged in to add evidence');
    
    const { data, error } = await client
      .from('evidence')
      .insert({
        mystery_id: evidence.mystery_id,
        title: evidence.title,
        description: evidence.description,
        impact: evidence.impact || Math.floor(Math.random() * 30), // Random impact if not provided
        added_by: user.value.id
      })
      .select()
      .single();
      
    if (error) throw error;
    
    return data;
  }
  
  return {
    getMysteries,
    getMystery,
    createMystery,
    runAnalysis,
    addComment,
    addEvidence
  };
};