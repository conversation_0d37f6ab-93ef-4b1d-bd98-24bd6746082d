import { ref } from 'vue';

export const useScraper = () => {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const scrapeUrl = async (url: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to scrape content');
      }

      const data = await response.json();
      return data.data;
    } catch (e) {
      error.value = e.message;
      throw e;
    } finally {
      loading.value = false;
    }
  };

  return {
    scrapeUrl,
    loading,
    error
  };
};