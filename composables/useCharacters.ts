import { Character } from '~/models/Character';

export const useCharacters = () => {
  const client = useSupabaseClient();
  const user = useSupabaseUser();
  
  // Get all characters
  async function getCharacters() {
    const { data, error } = await client
      .from('characters')
      .select(`
        *,
        character_tags (tag),
        character_attributes (key, value)
      `)
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    
    // Transform the data to match our Character model
    return data.map(char => {
      return {
        ...char,
        tags: char.character_tags?.map(t => t.tag) || [],
        attributes: char.character_attributes?.reduce((acc, attr) => {
          acc[attr.key] = attr.value;
          return acc;
        }, {}) || {}
      };
    });
  }
  
  // Get a single character by ID
  async function getCharacter(id: string) {
    const { data, error } = await client
      .from('characters')
      .select(`
        *,
        character_tags (tag),
        character_attributes (key, value),
        character_mysteries (
          id,
          role,
          mysteries (
            id, 
            title,
            description,
            category,
            created_at
          )
        )
      `)
      .eq('id', id)
      .single();
      
    if (error) throw error;
    
    // Transform the data
    const character = {
      ...data,
      tags: data.character_tags?.map(t => t.tag) || [],
      attributes: data.character_attributes?.reduce((acc, attr) => {
        acc[attr.key] = attr.value;
        return acc;
      }, {}) || {},
      mysteries: data.character_mysteries?.map(cm => ({
        ...cm.mysteries,
        role: cm.role
      })) || []
    };
    
    // Get timeline events for this character
    const { data: timelineData, error: timelineError } = await client
      .from('timeline_events')
      .select('*')
      .eq('character_id', id)
      .order('event_date', { ascending: false });
      
    if (timelineError) throw timelineError;
    
    character.timeline = timelineData || [];
    
    return character;
  }
  
  // Create a new character
  async function createCharacter(character: Character) {
    if (!user.value) throw new Error('You must be logged in to create a character');
    
    // First, create the character
    const { data, error } = await client
      .from('characters')
      .insert({
        name: character.name,
        description: character.description,
        background: character.background,
        image_url: character.image_url,
        wiki_url: character.wiki_url,
        created_by: user.value.id
      })
      .select()
      .single();
      
    if (error) throw error;
    
    const characterId = data.id;
    
    // Add tags if they exist
    if (character.tags && character.tags.length > 0) {
      const tagInserts = character.tags.map(tag => ({
        character_id: characterId,
        tag
      }));
      
      const { error: tagError } = await client
        .from('character_tags')
        .insert(tagInserts);
        
      if (tagError) throw tagError;
    }
    
    // Add attributes if they exist
    if (character.attributes && Object.keys(character.attributes).length > 0) {
      const attrInserts = Object.entries(character.attributes).map(([key, value]) => ({
        character_id: characterId,
        key,
        value
      }));
      
      const { error: attrError } = await client
        .from('character_attributes')
        .insert(attrInserts);
        
      if (attrError) throw attrError;
    }
    
    // Add an initial timeline event
    const { error: timelineError } = await client
      .from('timeline_events')
      .insert({
        character_id: characterId,
        title: 'Character Created',
        description: `${character.name} was added to the database.`,
        created_by: user.value.id
      });
      
    if (timelineError) throw timelineError;
    
    return characterId;
  }
  
  // Extract character data from wiki content using AI
  async function extractCharacterData(wikiContent: string, wikiUrl: string = '') {
    // This is where we would call an AI service to extract character data
    // For now, we'll simulate the process with a timeout
    
    return new Promise<Partial<Character>>((resolve) => {
      setTimeout(() => {
        // Return mock extracted data
        resolve({
          name: 'Extracted Character Name',
          description: 'This is an automatically extracted description based on the wiki content.',
          background: 'This is the automatically extracted background information detailing the character\'s history.',
          tags: ['detective', 'fictional', 'intelligent'],
          attributes: {
            occupation: 'Detective',
            residence: 'London',
            creator: 'Author Name'
          }
        });
      }, 2000);
    });
  }
  
  return {
    getCharacters,
    getCharacter,
    createCharacter,
    extractCharacterData
  };
};